# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Repository Overview

This is a comprehensive data intelligence platform with multiple applications built using Flask, Python, and various data processing libraries. The codebase consists of modular services covering search systems, business intelligence, sales prediction, speech processing, and data pipeline management.

## Core Architecture

### Main System Components

1. **搜索系统 (Search System)** - Search/intelligence engine
   - Location: `/搜索系统/`
   - Flask web application for product search with Elasticsearch
   - Implements FAISS for vector-based search
   - Includes keyword performance analytics and A/B testing capability

2. **CRM-ChatBI** - Business intelligence dashboard
   - Location: `/CRM-ChatBI/`
   - CRM data analysis with language-based queries
   - Feishu/bitab integration and user authentication
   - Real-time store performance monitoring

3. **销量预测 (Sales Prediction)**
   - Location: `/销量预测/`
   - Machine learning models for daily/weekly sales forecasting
   - Uses ODPS for big data processing
   - Implements multiprocessing for performance

4. **语音分析 (Speech Analysis)**
   - Location: `/语音分析/`
   - Audio processing with Whisper AI
   - Speech-to-text and voice analysis capabilities

5. **爬虫商品匹配 (Data Scraping & Matching)**
   - Location: `/爬虫商品匹配/`
   - Web scraping for competitor analysis
   - Product/SKU matching algorithms
   - Uses FAISS for similarity matching

### Key Technical Stack

**Backend Services:**
- Flask framework for web applications
- Elasticsearch for full-text search
- FAISS for vector similarity search
- OpenAI APIs for AI capabilities
- LangChain for LLM orchestration

**Data Processing:**
- Python pandas for data manipulation
- ODPS for big data processing
- PyODPS for ODPS integration
- DuckDB for analytics

**Infrastructure:**
- Background jobs via launchd (macOS)
- File-based logging system
- Git-based deployment
- Environment-based configuration

## Development Commands

### Environment Setup
```bash
# Create virtual environment
python3 -m venv venv
source venv/bin/activate  # macOS/Linux

# Install base dependencies
pip install -r requirements.txt

# Install system-specific dependencies
cd 搜索系统 && pip install -r requirements.txt
cd CRM-ChatBI && pip install -r requirements.txt
```

### Running Services

**Search System:**
```bash
cd 搜索系统
nohup python app.py > ../logs/search_arena.log 2>&1 &
# Or use restart script: ./restart_search.sh
```

**CRM-ChatBI:**
```bash
cd CRM-ChatBI
python app.py
```

**Sales Prediction:**
```bash
cd 销量预测
python predict_day_multi_process.py
python predict_week_multi_process.py
```

**Voice Processing:**
```bash
cd 语音分析
python Speech-recgonize-whisper-Only.py
```

### Background Job Management
```bash
# Reload all background jobs
./reload_all_launchd_jobs.sh

# Check job status
launchctl list | grep com.xianmu

# Specific job restart
launchctl unload ~/Library/LaunchAgents/com.xianmu.predict.day-only.plist
launchctl load ~/Library/LaunchAgents/com.xianmu.predict.day-only.plist
```

### Database & Search Operations

**ODPS Operations:**
- Connection via `odps_client.py`
- Uses environment variables for credentials
- Big data processing for sales/CRM data

**Search Testing:**
```bash
# Elasticsearch Direct Access
cd 搜索系统
python search_xianmu_directly_from_es.py

# FAISS Vector Search Testing
python search-with-FAISS.py
```

## Configuration Patterns

**Environment Variables:**
- `OPENAI_API_KEY` - API key access
- `AZURE_OPENAI_ENDPOINT` - Azure OpenAI configuration
- ODPS connections via environment config
- Feishu webhook tokens

**File Structure Patterns:**
- Core app.py files use `nohup` for background processing
- PID tracking in *.pid.log files
- Log standardization via ../logs/
- LaunchD plist files in ~/Library/LaunchAgents/

## Testing Commands

1. **Search System Test:**
   ```bash
   curl "http://localhost:5000/search?query=芒果&city=杭州&page_size=20"
   ```

2. **CRM Test:**
   ```bash
   curl "http://localhost:5001/"  # After login
   ```

3. **Data Pipeline Verification:**
   Check log files: `tail -f logs/search_arena.log`
   Monitor launchd status: `launchctl list | grep xianmu`

## File Type Conventions

- **Notebooks:** .ipynb files for data analysis (pandas, matplotlib)
- **Scripts:** .py files for production services
- **Configuration:** LaunchD plist files for scheduling
- **Logs:** Centralized in `/logs/` directory with .log extension
- **Data:** CSV, JSON, Markdown files in component directories
- **Templates:** Jinja2 HTML templates in `/templates/` folders

## Performance Monitoring

- Real-time search performance via `/logs/search_arena.log`
- Prediction model accuracy in `/销量预测/线上预测结果比对.ipynb`
- Search keyword performance in `搜索系统/线上query点击率分析.ipynb`

## Integration Points

- **Feishu Integration:** Via `feishu_client.py`
- **Voice Processing:** Via `speech_recognize.sh` cron job
- **CRM Integration:** Through ODPS -> Flask API -> Frontend
- **Data Pipeline:** Scheduled via launchd -> Python scripts -> ODPS
{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["from datetime import datetime\n", "import requests\n", "\n", "\n", "url = (\n", "    \"https://open.feishu.cn/open-apis/bot/v2/hook/aaf777eb-819c-4701-bc4b-893bf02addfc\"\n", ")\n", "\n", "\n", "def send_feishu_notice_with_title_and_content(\n", "    markdown_str: str,\n", "    feishu_url=url,\n", "    title=\"\",\n", "    error=False,\n", "):\n", "    feishu_message_obj = {\n", "        \"schema\": \"2.0\",\n", "        \"header\": {\n", "            \"template\": \"red\" if error else \"blue\",\n", "            \"title\": {\n", "                \"content\": f\"**{title}**\",\n", "                \"tag\": \"lark_md\",\n", "            },\n", "        },\n", "        \"body\": {\n", "            \"elements\": [\n", "                {\n", "                    \"tag\": \"markdown\",\n", "                    \"content\": markdown_str,\n", "                },\n", "                {\n", "                    \"tag\": \"markdown\",\n", "                    \"content\": f\"> 数据生成于:{datetime.now().strftime('%Y-%m-%d %H:%M')}\\n> \",\n", "                },\n", "            ]\n", "        },\n", "    }\n", "    headers = {\"Content-Type\": \"application/json\"}\n", "    data = {\"msg_type\": \"interactive\", \"card\": feishu_message_obj}\n", "    feishu_result = requests.post(\n", "        url=feishu_url, json=data, headers=headers, verify=False, proxies={}\n", "    ).json()\n", "    return feishu_result"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-07-31 17:52:45 - INFO - Instance ID: 20250731095233922gym95gdidlr\n", "  Log view: http://logview.odps.aliyun.com/logview/?h=http://service.cn-hangzhou.maxcompute.aliyun.com/api&p=summerfarm_ds_dev&i=20250731095233922gym95gdidlr&token=T3B4SzA1SUJ4ZlZwdVpiSWNmYU5GbTRWdk1BPSxPRFBTX09CTzpwNF8yMTMyMTQ5NzU2NjcwNjcwMjQsMTc1NjU0NzU2NSx7IlN0YXRlbWVudCI6W3siQWN0aW9uIjpbIm9kcHM6UmVhZCJdLCJFZmZlY3QiOiJBbGxvdyIsIlJlc291cmNlIjpbImFjczpvZHBzOio6cHJvamVjdHMvc3VtbWVyZmFybV9kc19kZXYvaW5zdGFuY2VzLzIwMjUwNzMxMDk1MjMzOTIyZ3ltOTVnZGlkbHIiXX1dLCJWZXJzaW9uIjoiMSJ9\n", "2025-07-31 17:53:14 - INFO - Tunnel session created: <InstanceDownloadSession id=202507311753135b1f3b1a07994575 project_name=summerfarm_ds_dev instance_id=20250731095233922gym95gdidlr>\n", "2025-07-31 17:53:40 - INFO - sql:\n", "\n", "SELECT  cust_id\n", "        ,count(distinct ds) 搜索天数\n", "        ,COUNT(DISTINCT CASE WHEN envent_type = 'impression' and CAST(idx AS BIGINT)<=1 THEN CONCAT(time,query) END) AS search_cnt\n", "        ,COUNT(distinct query) AS query_cnt\n", "        ,COUNT(DISTINCT CASE WHEN envent_type = 'click' THEN CONCAT(time,query,idx) END) AS clicked_cnt\n", "        ,COUNT(DISTINCT CASE WHEN envent_type = 'impression' THEN CONCAT(time,query,idx) END) AS impression_cnt\n", "FROM    summerfarm_tech.app_log_search_detail_di\n", "WHERE   ds BETWEEN '20250628' and '20250729'\n", "GROUP BY cust_id\n", ";\n", "\n", "columns:Index(['cust_id', '搜索天数', 'search_cnt', 'query_cnt', 'clicked_cnt',\n", "       'impression_cnt'],\n", "      dtype='object')\n"]}], "source": ["from datetime import datetime, timedelta\n", "import sys\n", "\n", "sys.path.append(\"../\")\n", "\n", "# category_prediction_df 获取\n", "from odps_client import get_odps_sql_result_as_df\n", "\n", "\n", "last_n_days = 30\n", "last_half_n_days = int(last_n_days / 2)\n", "ds_yesterday = (datetime.now() - timedelta(days=1)).strftime(\"%Y%m%d\")\n", "last_n_days_ago = (datetime.now() - timedelta(days=last_n_days)).strftime(\"%Y%m%d\")\n", "last_half_n_days_ago = (datetime.now() - <PERSON><PERSON><PERSON>(last_half_n_days)).strftime(\"%Y%m%d\")\n", "\n", "# 此处为了还原，写死日期范围：\n", "last_n_days_ago = \"20250628\"\n", "ds_yesterday = \"20250729\"\n", "\n", "user_static = f\"\"\"\n", "SELECT  cust_id\n", "        ,count(distinct ds) 搜索天数\n", "        ,COUNT(DISTINCT CASE WHEN envent_type = 'impression' and CAST(idx AS BIGINT)<=1 THEN CONCAT(time,query) END) AS search_cnt\n", "        ,COUNT(distinct query) AS query_cnt\n", "        ,COUNT(DISTINCT CASE WHEN envent_type = 'click' THEN CONCAT(time,query,idx) END) AS clicked_cnt\n", "        ,COUNT(DISTINCT CASE WHEN envent_type = 'impression' THEN CONCAT(time,query,idx) END) AS impression_cnt\n", "FROM    summerfarm_tech.app_log_search_detail_di\n", "WHERE   ds BETWEEN '{last_n_days_ago}' and '{ds_yesterday}'\n", "GROUP BY cust_id\n", ";\n", "\"\"\"\n", "\n", "user_static_df = get_odps_sql_result_as_df(sql=user_static)"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>cust_id</th>\n", "      <th>搜索天数</th>\n", "      <th>search_cnt</th>\n", "      <th>query_cnt</th>\n", "      <th>clicked_cnt</th>\n", "      <th>impression_cnt</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>count</th>\n", "      <td>56670.000000</td>\n", "      <td>56671.000000</td>\n", "      <td>56671.000000</td>\n", "      <td>56671.000000</td>\n", "      <td>56671.000000</td>\n", "      <td>56671.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>mean</th>\n", "      <td>359425.709546</td>\n", "      <td>4.387235</td>\n", "      <td>11.693759</td>\n", "      <td>6.945245</td>\n", "      <td>10.634822</td>\n", "      <td>101.369625</td>\n", "    </tr>\n", "    <tr>\n", "      <th>std</th>\n", "      <td>168107.634820</td>\n", "      <td>4.876399</td>\n", "      <td>24.005382</td>\n", "      <td>11.457263</td>\n", "      <td>24.449682</td>\n", "      <td>188.698854</td>\n", "    </tr>\n", "    <tr>\n", "      <th>min</th>\n", "      <td>2.000000</td>\n", "      <td>1.000000</td>\n", "      <td>0.000000</td>\n", "      <td>1.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>25%</th>\n", "      <td>232080.250000</td>\n", "      <td>1.000000</td>\n", "      <td>2.000000</td>\n", "      <td>2.000000</td>\n", "      <td>1.000000</td>\n", "      <td>14.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>50%</th>\n", "      <td>404927.000000</td>\n", "      <td>2.000000</td>\n", "      <td>5.000000</td>\n", "      <td>4.000000</td>\n", "      <td>4.000000</td>\n", "      <td>40.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>75%</th>\n", "      <td>508994.500000</td>\n", "      <td>6.000000</td>\n", "      <td>12.000000</td>\n", "      <td>8.000000</td>\n", "      <td>11.000000</td>\n", "      <td>111.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>max</th>\n", "      <td>562050.000000</td>\n", "      <td>32.000000</td>\n", "      <td>801.000000</td>\n", "      <td>375.000000</td>\n", "      <td>1015.000000</td>\n", "      <td>4992.000000</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["             cust_id          搜索天数    search_cnt     query_cnt   clicked_cnt  \\\n", "count   56670.000000  56671.000000  56671.000000  56671.000000  56671.000000   \n", "mean   359425.709546      4.387235     11.693759      6.945245     10.634822   \n", "std    168107.634820      4.876399     24.005382     11.457263     24.449682   \n", "min         2.000000      1.000000      0.000000      1.000000      0.000000   \n", "25%    232080.250000      1.000000      2.000000      2.000000      1.000000   \n", "50%    404927.000000      2.000000      5.000000      4.000000      4.000000   \n", "75%    508994.500000      6.000000     12.000000      8.000000     11.000000   \n", "max    562050.000000     32.000000    801.000000    375.000000   1015.000000   \n", "\n", "       impression_cnt  \n", "count    56671.000000  \n", "mean       101.369625  \n", "std        188.698854  \n", "min          0.000000  \n", "25%         14.000000  \n", "50%         40.000000  \n", "75%        111.000000  \n", "max       4992.000000  "]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["user_static_df.describe()"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["0.25     2.0\n", "0.50     5.0\n", "0.75    12.0\n", "0.90    28.0\n", "0.95    44.0\n", "0.99    99.3\n", "Name: search_cnt, dtype: float64\n", "搜索次数超过:5 的用户数:28872, 人数占比:50.95%, 搜索次数占比:91.53%, 点击次数占比:92.35%\n", "搜索次数超过:10 的用户数:17694, 人数占比:31.22%, 搜索次数占比:80.32%, 点击次数占比:81.8%\n", "搜索次数超过:15 的用户数:12210, 人数占比:21.55%, 搜索次数占比:70.58%, 点击次数占比:72.37%\n", "搜索次数超过:20 的用户数:8936, 人数占比:15.77%, 搜索次数占比:62.25%, 点击次数占比:64.21%\n", "搜索次数超过:25 的用户数:6768, 人数占比:11.94%, 搜索次数占比:55.11%, 点击次数占比:57.11%\n", "搜索次数超过:30 的用户数:5238, 人数占比:9.24%, 搜索次数占比:48.91%, 点击次数占比:50.82%\n", "搜索次数超过:35 的用户数:4137, 人数占比:7.3%, 搜索次数占比:43.62%, 点击次数占比:45.48%\n", "搜索次数超过:40 的用户数:3346, 人数占比:5.9%, 搜索次数占比:39.21%, 点击次数占比:41.01%\n", "搜索次数超过:45 的用户数:2732, 人数占比:4.82%, 搜索次数占比:35.33%, 点击次数占比:37.1%\n", "搜索次数超过:50 的用户数:2278, 人数占比:4.02%, 搜索次数占比:32.12%, 点击次数占比:33.76%\n", "搜索次数超过:55 的用户数:1890, 人数占比:3.34%, 搜索次数占比:29.08%, 点击次数占比:30.66%\n"]}], "source": ["print(user_static_df[\"search_cnt\"].quantile([0.25, 0.5, 0.75, 0.9, 0.95, 0.99]))\n", "total_user_search_cnt = user_static_df[\"search_cnt\"].sum()\n", "for search_cnt in range(5, 60, 5):\n", "    _df = user_static_df[user_static_df[\"search_cnt\"] >= search_cnt]\n", "    print(\n", "        f\"搜索次数超过:{search_cnt} 的用户数:{_df.shape[0]}, 人数占比:{round(_df.shape[0]*100.0/user_static_df.shape[0],2)}%, 搜索次数占比:{round(100.0*_df['search_cnt'].sum()/total_user_search_cnt,2)}%, 点击次数占比:{round(100.0*_df['clicked_cnt'].sum()/user_static_df['clicked_cnt'].sum(),2)}%\"\n", "    )"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>cust_id</th>\n", "      <th>搜索天数</th>\n", "      <th>search_cnt</th>\n", "      <th>query_cnt</th>\n", "      <th>clicked_cnt</th>\n", "      <th>impression_cnt</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>count</th>\n", "      <td>8389.000000</td>\n", "      <td>8390.000000</td>\n", "      <td>8390.000000</td>\n", "      <td>8390.000000</td>\n", "      <td>8390.0</td>\n", "      <td>8390.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>mean</th>\n", "      <td>380022.188580</td>\n", "      <td>1.239928</td>\n", "      <td>1.611323</td>\n", "      <td>1.570799</td>\n", "      <td>0.0</td>\n", "      <td>16.393683</td>\n", "    </tr>\n", "    <tr>\n", "      <th>std</th>\n", "      <td>164924.744872</td>\n", "      <td>0.635129</td>\n", "      <td>1.333948</td>\n", "      <td>1.069157</td>\n", "      <td>0.0</td>\n", "      <td>19.785965</td>\n", "    </tr>\n", "    <tr>\n", "      <th>min</th>\n", "      <td>26.000000</td>\n", "      <td>1.000000</td>\n", "      <td>0.000000</td>\n", "      <td>1.000000</td>\n", "      <td>0.0</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>25%</th>\n", "      <td>261526.000000</td>\n", "      <td>1.000000</td>\n", "      <td>1.000000</td>\n", "      <td>1.000000</td>\n", "      <td>0.0</td>\n", "      <td>4.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>50%</th>\n", "      <td>432936.000000</td>\n", "      <td>1.000000</td>\n", "      <td>1.000000</td>\n", "      <td>1.000000</td>\n", "      <td>0.0</td>\n", "      <td>10.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>75%</th>\n", "      <td>525368.000000</td>\n", "      <td>1.000000</td>\n", "      <td>2.000000</td>\n", "      <td>2.000000</td>\n", "      <td>0.0</td>\n", "      <td>20.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>max</th>\n", "      <td>562042.000000</td>\n", "      <td>19.000000</td>\n", "      <td>33.000000</td>\n", "      <td>28.000000</td>\n", "      <td>0.0</td>\n", "      <td>322.000000</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["             cust_id         搜索天数   search_cnt    query_cnt  clicked_cnt  \\\n", "count    8389.000000  8390.000000  8390.000000  8390.000000       8390.0   \n", "mean   380022.188580     1.239928     1.611323     1.570799          0.0   \n", "std    164924.744872     0.635129     1.333948     1.069157          0.0   \n", "min        26.000000     1.000000     0.000000     1.000000          0.0   \n", "25%    261526.000000     1.000000     1.000000     1.000000          0.0   \n", "50%    432936.000000     1.000000     1.000000     1.000000          0.0   \n", "75%    525368.000000     1.000000     2.000000     2.000000          0.0   \n", "max    562042.000000    19.000000    33.000000    28.000000          0.0   \n", "\n", "       impression_cnt  \n", "count     8390.000000  \n", "mean        16.393683  \n", "std         19.785965  \n", "min          0.000000  \n", "25%          4.000000  \n", "50%         10.000000  \n", "75%         20.000000  \n", "max        322.000000  "]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["user_static_df[user_static_df[\"clicked_cnt\"]<=0].describe()"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-07-31 17:53:51 - INFO - Instance ID: 20250731095340336gpp4gd30lbo\n", "  Log view: http://logview.odps.aliyun.com/logview/?h=http://service.cn-hangzhou.maxcompute.aliyun.com/api&p=summerfarm_ds_dev&i=20250731095340336gpp4gd30lbo&token=WmR1cUw5cUMydWpQc2xUWUx4NCtPUkVzUmg4PSxPRFBTX09CTzpwNF8yMTMyMTQ5NzU2NjcwNjcwMjQsMTc1NjU0NzYzMSx7IlN0YXRlbWVudCI6W3siQWN0aW9uIjpbIm9kcHM6UmVhZCJdLCJFZmZlY3QiOiJBbGxvdyIsIlJlc291cmNlIjpbImFjczpvZHBzOio6cHJvamVjdHMvc3VtbWVyZmFybV9kc19kZXYvaW5zdGFuY2VzLzIwMjUwNzMxMDk1MzQwMzM2Z3BwNGdkMzBsYm8iXX1dLCJWZXJzaW9uIjoiMSJ9\n", "2025-07-31 17:54:02 - INFO - 20250731095340336gpp4gd30lbo 2025-07-31 17:54:02 M1_job_0:0/1/1[100%]\tR2_1_job_0:0/1/1[100%]\t\n", "2025-07-31 17:54:03 - INFO - Tunnel session created: <InstanceDownloadSession id=2025073117540354203b1a07993f31 project_name=summerfarm_ds_dev instance_id=20250731095340336gpp4gd30lbo>\n", "2025-07-31 17:54:04 - INFO - sql:\n", "\n", "SELECT  query\n", "        ,COUNT(distinct cust_id) as searched_users\n", "        ,SUM(CASE WHEN envent_type = 'impression' and idx='0' THEN 1 ELSE 0 END) as searched_times\n", "        ,COUNT(DISTINCT CASE WHEN envent_type = 'impression' THEN CONCAT(time,query,idx,cust_id) END) AS impression_cnt\n", "        ,COUNT(DISTINCT CASE WHEN envent_type = 'click' THEN CONCAT(time,query,idx,cust_id) END) AS click_cnt\n", "        ,COUNT(DISTINCT CASE WHEN envent_type = 'click' THEN CONCAT(time,query,idx,cust_id) END)*1.00/COUNT(DISTINCT CASE WHEN envent_type = 'impression' THEN CONCAT(time,query,idx,cust_id) END) as sku_ctr\n", "        ,COUNT(DISTINCT CASE WHEN envent_type = 'click' THEN cust_id END) AS clicked_user_cnt\n", "        ,COUNT(DISTINCT CASE WHEN envent_type = 'click' THEN cust_id END)*1.00/COUNT(DISTINCT CASE WHEN envent_type = 'impression' THEN cust_id END) as user_ctr\n", "        ,PERCENTILE(CASE WHEN envent_type = 'click' THEN CAST(idx AS BIGINT) END,0.5) AS p50_click_index\n", "        ,PERCENTILE(CASE WHEN envent_type = 'click' THEN CAST(idx AS BIGINT) END,0.75) AS p75_click_index\n", "        ,PERCENTILE(CASE WHEN envent_type = 'click' THEN CAST(idx AS BIGINT) END,0.9) AS p90_click_index\n", "FROM    summerfarm_tech.app_log_search_detail_di\n", "WHERE   ds BETWEEN '20250628' and '20250729'\n", "GROUP BY query\n", ";\n", "\n", "columns:Index(['query', 'searched_users', 'searched_times', 'impression_cnt',\n", "       'click_cnt', 'sku_ctr', 'clicked_user_cnt', 'user_ctr',\n", "       'p50_click_index', 'p75_click_index', 'p90_click_index'],\n", "      dtype='object')\n"]}], "source": ["top_query_label = f\"\"\"\n", "SELECT  query\n", "        ,COUNT(distinct cust_id) as searched_users\n", "        ,SUM(CASE WHEN envent_type = 'impression' and idx='0' THEN 1 ELSE 0 END) as searched_times\n", "        ,COUNT(DISTINCT CASE WHEN envent_type = 'impression' THEN CONCAT(time,query,idx,cust_id) END) AS impression_cnt\n", "        ,COUNT(DISTINCT CASE WHEN envent_type = 'click' THEN CONCAT(time,query,idx,cust_id) END) AS click_cnt\n", "        ,COUNT(DISTINCT CASE WHEN envent_type = 'click' THEN CONCAT(time,query,idx,cust_id) END)*1.00/COUNT(DISTINCT CASE WHEN envent_type = 'impression' THEN CONCAT(time,query,idx,cust_id) END) as sku_ctr\n", "        ,COUNT(DISTINCT CASE WHEN envent_type = 'click' THEN cust_id END) AS clicked_user_cnt\n", "        ,COUNT(DISTINCT CASE WHEN envent_type = 'click' THEN cust_id END)*1.00/COUNT(DISTINCT CASE WHEN envent_type = 'impression' THEN cust_id END) as user_ctr\n", "        ,PERCENTILE(CASE WHEN envent_type = 'click' THEN CAST(idx AS BIGINT) END,0.5) AS p50_click_index\n", "        ,PERCENTILE(CASE WHEN envent_type = 'click' THEN CAST(idx AS BIGINT) END,0.75) AS p75_click_index\n", "        ,PERCENTILE(CASE WHEN envent_type = 'click' THEN CAST(idx AS BIGINT) END,0.9) AS p90_click_index\n", "FROM    summerfarm_tech.app_log_search_detail_di\n", "WHERE   ds BETWEEN '{last_n_days_ago}' and '{ds_yesterday}'\n", "GROUP BY query\n", ";\n", "\"\"\"\n", "\n", "top_query_labeled_df = get_odps_sql_result_as_df(sql=top_query_label)\n", "top_query_labeled_df[\"searched_times\"] = top_query_labeled_df[\"searched_times\"].apply(\n", "    lambda x: x if x is not None and x > 0 else 1\n", ")"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>searched_users</th>\n", "      <th>searched_times</th>\n", "      <th>impression_cnt</th>\n", "      <th>click_cnt</th>\n", "      <th>sku_ctr</th>\n", "      <th>clicked_user_cnt</th>\n", "      <th>user_ctr</th>\n", "      <th>p50_click_index</th>\n", "      <th>p75_click_index</th>\n", "      <th>p90_click_index</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>count</th>\n", "      <td>25540.000000</td>\n", "      <td>25540.000000</td>\n", "      <td>25540.000000</td>\n", "      <td>25540.000000</td>\n", "      <td>23467.000000</td>\n", "      <td>25540.000000</td>\n", "      <td>25540.000000</td>\n", "      <td>11878.000000</td>\n", "      <td>11878.000000</td>\n", "      <td>11878.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>mean</th>\n", "      <td>15.409789</td>\n", "      <td>25.856186</td>\n", "      <td>224.925646</td>\n", "      <td>23.597729</td>\n", "      <td>0.071465</td>\n", "      <td>9.950235</td>\n", "      <td>0.357694</td>\n", "      <td>5.944140</td>\n", "      <td>7.640870</td>\n", "      <td>9.626065</td>\n", "    </tr>\n", "    <tr>\n", "      <th>std</th>\n", "      <td>152.186819</td>\n", "      <td>344.710015</td>\n", "      <td>3280.697218</td>\n", "      <td>368.103507</td>\n", "      <td>0.120516</td>\n", "      <td>116.074075</td>\n", "      <td>0.421561</td>\n", "      <td>11.584597</td>\n", "      <td>13.366936</td>\n", "      <td>16.071603</td>\n", "    </tr>\n", "    <tr>\n", "      <th>min</th>\n", "      <td>1.000000</td>\n", "      <td>1.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>25%</th>\n", "      <td>1.000000</td>\n", "      <td>1.000000</td>\n", "      <td>4.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>1.000000</td>\n", "      <td>1.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>50%</th>\n", "      <td>1.000000</td>\n", "      <td>1.000000</td>\n", "      <td>11.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.002833</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>2.000000</td>\n", "      <td>3.000000</td>\n", "      <td>4.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>75%</th>\n", "      <td>3.000000</td>\n", "      <td>3.000000</td>\n", "      <td>34.000000</td>\n", "      <td>2.000000</td>\n", "      <td>0.107143</td>\n", "      <td>1.000000</td>\n", "      <td>0.800000</td>\n", "      <td>6.500000</td>\n", "      <td>9.000000</td>\n", "      <td>12.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>max</th>\n", "      <td>8693.000000</td>\n", "      <td>25697.000000</td>\n", "      <td>258447.000000</td>\n", "      <td>31172.000000</td>\n", "      <td>3.240000</td>\n", "      <td>7107.000000</td>\n", "      <td>1.000000</td>\n", "      <td>321.000000</td>\n", "      <td>321.000000</td>\n", "      <td>321.000000</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["       searched_users  searched_times  impression_cnt     click_cnt  \\\n", "count    25540.000000    25540.000000    25540.000000  25540.000000   \n", "mean        15.409789       25.856186      224.925646     23.597729   \n", "std        152.186819      344.710015     3280.697218    368.103507   \n", "min          1.000000        1.000000        0.000000      0.000000   \n", "25%          1.000000        1.000000        4.000000      0.000000   \n", "50%          1.000000        1.000000       11.000000      0.000000   \n", "75%          3.000000        3.000000       34.000000      2.000000   \n", "max       8693.000000    25697.000000   258447.000000  31172.000000   \n", "\n", "            sku_ctr  clicked_user_cnt      user_ctr  p50_click_index  \\\n", "count  23467.000000      25540.000000  25540.000000     11878.000000   \n", "mean       0.071465          9.950235      0.357694         5.944140   \n", "std        0.120516        116.074075      0.421561        11.584597   \n", "min        0.000000          0.000000      0.000000         0.000000   \n", "25%        0.000000          0.000000      0.000000         0.000000   \n", "50%        0.002833          0.000000      0.000000         2.000000   \n", "75%        0.107143          1.000000      0.800000         6.500000   \n", "max        3.240000       7107.000000      1.000000       321.000000   \n", "\n", "       p75_click_index  p90_click_index  \n", "count     11878.000000     11878.000000  \n", "mean          7.640870         9.626065  \n", "std          13.366936        16.071603  \n", "min           0.000000         0.000000  \n", "25%           1.000000         1.000000  \n", "50%           3.000000         4.000000  \n", "75%           9.000000        12.000000  \n", "max         321.000000       321.000000  "]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["top_query_labeled_df.describe()"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/var/folders/b3/9hcz86fx1_z_8m4121xwbs2h0000gn/T/ipykernel_52560/3878181176.py:1: FutureWarning: A value is trying to be set on a copy of a DataFrame or Series through chained assignment using an inplace method.\n", "The behavior will change in pandas 3.0. This inplace method will never work because the intermediate object on which we are setting values always behaves as a copy.\n", "\n", "For example, when doing 'df[col].method(value, inplace=True)', try using 'df.method({col: value}, inplace=True)' or df[col] = df[col].method(value) instead, to perform the operation inplace on the original object.\n", "\n", "\n", "  top_query_labeled_df[\"searched_times\"].fillna(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["0.10     1.0\n", "0.25     1.0\n", "0.50     1.0\n", "0.75     3.0\n", "0.90    11.0\n", "0.95    34.0\n", "Name: searched_users, dtype: float64\n", "0.10     1.0\n", "0.25     1.0\n", "0.50     1.0\n", "0.75     3.0\n", "0.90    14.0\n", "0.95    42.0\n", "Name: searched_times, dtype: float64\n", "\n", "Searched Users 分位数统计:\n", "count    25540.000000\n", "mean        15.409789\n", "std        152.186819\n", "min          1.000000\n", "5%           1.000000\n", "25%          1.000000\n", "50%          1.000000\n", "75%          3.000000\n", "90%         11.000000\n", "95%         34.000000\n", "99%        248.000000\n", "max       8693.000000\n", "Name: searched_users, dtype: float64\n", "total_times:1.0\n", "\n", "Searched Times 分位数统计:\n", "count    25540.000000\n", "mean        25.856186\n", "std        344.710015\n", "min          1.000000\n", "5%           1.000000\n", "25%          1.000000\n", "50%          1.000000\n", "75%          3.000000\n", "90%         14.000000\n", "95%         42.000000\n", "99%        388.220000\n", "max      25697.000000\n", "Name: searched_times, dtype: float64\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1200x800 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["top_query_labeled_df[\"searched_times\"].fillna(\n", "    1, inplace=True\n", ")  # 默认肯定有至少1次搜索, inplace=True to modify the DataFrame directly\n", "top_query_labeled_df[\"searched_times\"] = top_query_labeled_df[\"searched_times\"].astype(\n", "    int\n", ")\n", "\n", "print(\n", "    top_query_labeled_df[\"searched_users\"].quantile([0.1, 0.25, 0.5, 0.75, 0.90, 0.95])\n", ")\n", "print(\n", "    top_query_labeled_df[\"searched_times\"].quantile([0.1, 0.25, 0.5, 0.75, 0.90, 0.95])\n", ")\n", "\n", "import matplotlib.pyplot as plt\n", "import numpy as np  # 导入 numpy\n", "\n", "# 设置中文字体显示 - Mac系统\n", "plt.rcParams[\"font.sans-serif\"] = [\n", "    \"Arial Unicode MS\"\n", "]  # Mac系统使用Arial Unicode MS字体\n", "plt.rcParams[\"axes.unicode_minus\"] = False  # 用来正常显示负号\n", "\n", "quantiles = [5, 25, 50, 75, 90, 95, 99]  # 修改分位数值\n", "fig, axes = plt.subplots(2, 1, figsize=(12,8))\n", "\n", "# Cumulative distribution of searched_users\n", "searched_users_counts = top_query_labeled_df[\"searched_users\"].sort_values()\n", "cumulative_users = np.arange(1, len(searched_users_counts) + 1) / len(\n", "    searched_users_counts\n", ")  # 使用 numpy 创建累积分布\n", "total_users = cumulative_users[-1]  #  直接取最后一个元素\n", "axes[0].semilogx(\n", "    searched_users_counts.values, cumulative_users, \"b-\", label=\"累积分布\"\n", ")  # 使用 semilogx 绘制对数坐标轴\n", "\n", "for p in quantiles:  # 遍历新的分位数\n", "    value = np.percentile(searched_users_counts, p)\n", "    axes[0].axvline(x=value, linestyle=\"--\", alpha=0.3)\n", "    axes[0].text(value, 0.5, f\"P{p}={int(value)}\", rotation=90)  # 添加分位数文本\n", "\n", "axes[0].set_xlabel(\"Searched Users (对数尺度)\")  # 修改 x 轴标签，并添加中文注释\n", "axes[0].set_ylabel(\"累积比例\")  # 修改 y 轴标签\n", "axes[0].set_title(\"Searched Users 的累积分布图\")  # 修改标题，并添加中文注释\n", "axes[0].grid(True, alpha=0.3)  # 添加网格\n", "axes[0].legend()  # 显示图例\n", "\n", "\n", "# 添加 searched_users 的统计信息\n", "stats_text_users = f\"\"\"\n", "统计信息:\n", "平均值: {int(top_query_labeled_df['searched_users'].mean())}\n", "中位数: {int(top_query_labeled_df['searched_users'].median())}\n", "最大值: {int(top_query_labeled_df['searched_users'].max())}\n", "最小值: {int(top_query_labeled_df['searched_users'].min())}\n", "\"\"\"\n", "axes[0].text(\n", "    0.02,\n", "    0.98,\n", "    stats_text_users,\n", "    transform=axes[0].transAxes,\n", "    verticalalignment=\"top\",\n", "    bbox=dict(boxstyle=\"round\", facecolor=\"white\", alpha=0.8),\n", ")\n", "\n", "# 打印 searched_users 的具体分位数值\n", "print(\"\\nSearched Users 分位数统计:\")  # 添加中文注释\n", "print(\n", "    top_query_labeled_df[\"searched_users\"].describe(\n", "        percentiles=[q / 100 for q in quantiles]\n", "    )\n", ")  # 使用 describe 打印分位数\n", "\n", "\n", "# Cumulative distribution of searched_times\n", "searched_times_counts = top_query_labeled_df[\"searched_times\"].sort_values()\n", "cumulative_times = np.arange(1, len(searched_times_counts) + 1) / len(\n", "    searched_times_counts\n", ")  # 使用 numpy 创建累积分布\n", "total_times = cumulative_times[-1]  # 直接取最后一个元素\n", "print(f\"total_times:{total_times}\")\n", "\n", "\n", "axes[1].semilogx(\n", "    searched_times_counts.values, cumulative_times, \"b-\", label=\"累积分布\"\n", ")  # 使用 semilogx 绘制对数坐标轴\n", "for p in quantiles:  # 遍历分位数\n", "    value = np.percentile(searched_times_counts, p)\n", "    axes[1].axvline(x=value, linestyle=\"--\", alpha=0.3)\n", "    axes[1].text(value, 0.5, f\"P{p}={int(value)}\", rotation=90)  # 添加分位数文本\n", "\n", "axes[1].set_xlabel(\"Searched Times (对数尺度)\")  # 修改 x 轴标签，并添加中文注释\n", "axes[1].set_ylabel(\"累积比例\")  # 修改 y 轴标签\n", "axes[1].set_title(\"Searched Times 的累积分布图\")  # 修改标题，并添加中文注释\n", "axes[1].grid(True, alpha=0.3)  # 添加网格\n", "axes[1].legend()  # 显示图例\n", "\n", "\n", "# 添加 searched_times 的统计信息\n", "stats_text_times = f\"\"\"\n", "统计信息:\n", "平均值: {int(top_query_labeled_df['searched_times'].mean())}\n", "中位数: {int(top_query_labeled_df['searched_times'].median())}\n", "最大值: {int(top_query_labeled_df['searched_times'].max())}\n", "最小值: {int(top_query_labeled_df['searched_times'].min())}\n", "\"\"\"\n", "axes[1].text(\n", "    0.02,\n", "    0.98,\n", "    stats_text_times,\n", "    transform=axes[1].transAxes,\n", "    verticalalignment=\"top\",\n", "    bbox=dict(boxstyle=\"round\", facecolor=\"white\", alpha=0.8),\n", ")\n", "\n", "# 打印 searched_times 的具体分位数值\n", "print(\"\\nSearched Times 分位数统计:\")  # 添加中文注释\n", "print(\n", "    top_query_labeled_df[\"searched_times\"].describe(\n", "        percentiles=[q / 100 for q in quantiles]\n", "    )\n", ")  # 使用 describe 打印分位数\n", "\n", "\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["搜索次数大于等于10 的词的总搜索次数占比: 93.89%\n", "搜索次数大于等于20 的词的总搜索次数占比: 91.62%\n", "搜索次数大于等于30 的词的总搜索次数占比: 89.97%\n", "搜索次数大于等于40 的词的总搜索次数占比: 88.57%\n", "搜索次数大于等于50 的词的总搜索次数占比: 87.41%\n", "搜索次数大于等于60 的词的总搜索次数占比: 86.29%\n", "搜索次数大于等于70 的词的总搜索次数占比: 85.26%\n", "搜索次数大于等于80 的词的总搜索次数占比: 84.20%\n", "搜索次数大于等于90 的词的总搜索次数占比: 83.26%\n", "搜索次数大于等于100 的词的总搜索次数占比: 82.56%\n", "搜索次数大于等于110 的词的总搜索次数占比: 81.77%\n", "搜索次数大于等于120 的词的总搜索次数占比: 80.92%\n", "搜索次数大于等于130 的词的总搜索次数占比: 80.22%\n", "搜索次数大于等于140 的词的总搜索次数占比: 79.57%\n", "搜索次数大于等于150 的词的总搜索次数占比: 79.13%\n", "搜索次数大于等于160 的词的总搜索次数占比: 78.90%\n", "搜索次数大于等于170 的词的总搜索次数占比: 78.20%\n", "搜索次数大于等于180 的词的总搜索次数占比: 77.54%\n", "搜索次数大于等于190 的词的总搜索次数占比: 77.12%\n", "搜索次数大于等于200 的词的总搜索次数占比: 76.73%\n", "搜索次数大于等于210 的词的总搜索次数占比: 76.26%\n", "搜索次数大于等于220 的词的总搜索次数占比: 75.75%\n", "搜索次数大于等于230 的词的总搜索次数占比: 75.30%\n", "搜索次数大于等于240 的词的总搜索次数占比: 74.59%\n", "搜索次数大于等于250 的词的总搜索次数占比: 73.97%\n", "搜索次数大于等于260 的词的总搜索次数占比: 73.74%\n", "搜索次数大于等于270 的词的总搜索次数占比: 73.33%\n", "搜索次数大于等于280 的词的总搜索次数占比: 73.04%\n", "搜索次数大于等于290 的词的总搜索次数占比: 72.96%\n", "total_search_times:660367\n"]}], "source": ["total_search_times = top_query_labeled_df[\"searched_times\"].sum()\n", "for i in range(10, 300, 10):\n", "    top_n = top_query_labeled_df[top_query_labeled_df[\"searched_times\"] >= i]\n", "    top_n_sum = top_n[\"searched_times\"].sum()\n", "    print(f\"搜索次数大于等于{i} 的词的总搜索次数占比: {top_n_sum / total_search_times:.2%}\")\n", "print(f\"total_search_times:{total_search_times}\")"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"data": {"text/plain": ["count    25540.000000\n", "mean        25.856186\n", "std        344.710015\n", "min          1.000000\n", "25%          1.000000\n", "50%          1.000000\n", "75%          3.000000\n", "max      25697.000000\n", "Name: searched_times, dtype: float64"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["top_query_labeled_df[\"searched_times\"].describe()"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"data": {"text/plain": ["0.250       1.000\n", "0.500       1.000\n", "0.750       3.000\n", "0.900      14.000\n", "0.950      42.000\n", "0.990     388.220\n", "0.995     810.220\n", "0.999    3870.256\n", "Name: searched_times, dtype: float64"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["top_query_labeled_df[\"searched_times\"].quantile([0.25,0.5,0.75,.90,0.95,0.99,0.995,0.999])"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>query_count</th>\n", "      <th>total_searched_times</th>\n", "      <th>search_times_share</th>\n", "      <th>query_count_share</th>\n", "    </tr>\n", "    <tr>\n", "      <th>搜索频次标签</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>中频搜索词</th>\n", "      <td>2792</td>\n", "      <td>123686</td>\n", "      <td>0.187299</td>\n", "      <td>0.109319</td>\n", "    </tr>\n", "    <tr>\n", "      <th>低频搜索词(&lt;10)</th>\n", "      <td>22371</td>\n", "      <td>40323</td>\n", "      <td>0.061062</td>\n", "      <td>0.875920</td>\n", "    </tr>\n", "    <tr>\n", "      <th>高频搜索词(&gt;230)</th>\n", "      <td>377</td>\n", "      <td>496358</td>\n", "      <td>0.751640</td>\n", "      <td>0.014761</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["             query_count  total_searched_times  search_times_share  \\\n", "搜索频次标签                                                               \n", "中频搜索词               2792                123686            0.187299   \n", "低频搜索词(<10)         22371                 40323            0.061062   \n", "高频搜索词(>230)          377                496358            0.751640   \n", "\n", "             query_count_share  \n", "搜索频次标签                          \n", "中频搜索词                 0.109319  \n", "低频搜索词(<10)            0.875920  \n", "高频搜索词(>230)           0.014761  "]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["top_query_labeled_df[\"搜索频次标签\"] = top_query_labeled_df[\"searched_times\"].apply(\n", "    lambda x: \"高频搜索词(>230)\" if x > 230 else \"低频搜索词(<10)\" if x < 10 else \"中频搜索词\"\n", ")\n", "\n", "# 根据\"搜索频次标签\"分组，并计算每个标签下的query数量和搜索次数总和\n", "frequency_group = top_query_labeled_df.groupby(\"搜索频次标签\").agg(\n", "    query_count=(\"query\", \"count\"),  # 计算每个标签下的query数量\n", "    total_searched_times=(\"searched_times\", \"sum\"),  # 计算每个标签下的搜索次数总和\n", ")\n", "\n", "# 计算每个分组的搜索次数占比\n", "frequency_group['search_times_share'] = frequency_group['total_searched_times'] / total_search_times\n", "# 计算每个分组的query数量占比\n", "frequency_group['query_count_share'] = frequency_group['query_count'] / top_query_labeled_df.shape[0]\n", "frequency_group\n"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>searched_users</th>\n", "      <th>searched_times</th>\n", "      <th>impression_cnt</th>\n", "      <th>click_cnt</th>\n", "      <th>sku_ctr</th>\n", "      <th>clicked_user_cnt</th>\n", "      <th>user_ctr</th>\n", "      <th>p50_click_index</th>\n", "      <th>p75_click_index</th>\n", "      <th>p90_click_index</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>count</th>\n", "      <td>377.000000</td>\n", "      <td>377.000000</td>\n", "      <td>377.000000</td>\n", "      <td>377.000000</td>\n", "      <td>377.000000</td>\n", "      <td>377.000000</td>\n", "      <td>377.000000</td>\n", "      <td>377.000000</td>\n", "      <td>377.000000</td>\n", "      <td>377.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>mean</th>\n", "      <td>693.533156</td>\n", "      <td>1316.599469</td>\n", "      <td>11164.925729</td>\n", "      <td>1285.111406</td>\n", "      <td>0.135718</td>\n", "      <td>502.193634</td>\n", "      <td>0.695185</td>\n", "      <td>1.907162</td>\n", "      <td>4.675729</td>\n", "      <td>9.200531</td>\n", "    </tr>\n", "    <tr>\n", "      <th>std</th>\n", "      <td>1044.178591</td>\n", "      <td>2519.541211</td>\n", "      <td>24625.963755</td>\n", "      <td>2749.906656</td>\n", "      <td>0.069264</td>\n", "      <td>814.232191</td>\n", "      <td>0.148872</td>\n", "      <td>3.578805</td>\n", "      <td>7.221592</td>\n", "      <td>12.750949</td>\n", "    </tr>\n", "    <tr>\n", "      <th>min</th>\n", "      <td>98.000000</td>\n", "      <td>231.000000</td>\n", "      <td>294.000000</td>\n", "      <td>22.000000</td>\n", "      <td>0.008498</td>\n", "      <td>25.000000</td>\n", "      <td>0.089928</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>25%</th>\n", "      <td>223.000000</td>\n", "      <td>334.000000</td>\n", "      <td>2578.000000</td>\n", "      <td>296.000000</td>\n", "      <td>0.092479</td>\n", "      <td>140.000000</td>\n", "      <td>0.644578</td>\n", "      <td>0.000000</td>\n", "      <td>1.000000</td>\n", "      <td>3.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>50%</th>\n", "      <td>351.000000</td>\n", "      <td>570.000000</td>\n", "      <td>4434.000000</td>\n", "      <td>552.000000</td>\n", "      <td>0.123623</td>\n", "      <td>241.000000</td>\n", "      <td>0.719636</td>\n", "      <td>1.000000</td>\n", "      <td>3.000000</td>\n", "      <td>5.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>75%</th>\n", "      <td>665.000000</td>\n", "      <td>1052.000000</td>\n", "      <td>8560.000000</td>\n", "      <td>1059.000000</td>\n", "      <td>0.176007</td>\n", "      <td>488.000000</td>\n", "      <td>0.789976</td>\n", "      <td>2.000000</td>\n", "      <td>5.000000</td>\n", "      <td>10.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>max</th>\n", "      <td>8693.000000</td>\n", "      <td>25697.000000</td>\n", "      <td>258447.000000</td>\n", "      <td>31172.000000</td>\n", "      <td>0.542334</td>\n", "      <td>7107.000000</td>\n", "      <td>0.951036</td>\n", "      <td>38.000000</td>\n", "      <td>69.250000</td>\n", "      <td>107.500000</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["       searched_users  searched_times  impression_cnt     click_cnt  \\\n", "count      377.000000      377.000000      377.000000    377.000000   \n", "mean       693.533156     1316.599469    11164.925729   1285.111406   \n", "std       1044.178591     2519.541211    24625.963755   2749.906656   \n", "min         98.000000      231.000000      294.000000     22.000000   \n", "25%        223.000000      334.000000     2578.000000    296.000000   \n", "50%        351.000000      570.000000     4434.000000    552.000000   \n", "75%        665.000000     1052.000000     8560.000000   1059.000000   \n", "max       8693.000000    25697.000000   258447.000000  31172.000000   \n", "\n", "          sku_ctr  clicked_user_cnt    user_ctr  p50_click_index  \\\n", "count  377.000000        377.000000  377.000000       377.000000   \n", "mean     0.135718        502.193634    0.695185         1.907162   \n", "std      0.069264        814.232191    0.148872         3.578805   \n", "min      0.008498         25.000000    0.089928         0.000000   \n", "25%      0.092479        140.000000    0.644578         0.000000   \n", "50%      0.123623        241.000000    0.719636         1.000000   \n", "75%      0.176007        488.000000    0.789976         2.000000   \n", "max      0.542334       7107.000000    0.951036        38.000000   \n", "\n", "       p75_click_index  p90_click_index  \n", "count       377.000000       377.000000  \n", "mean          4.675729         9.200531  \n", "std           7.221592        12.750949  \n", "min           0.000000         0.000000  \n", "25%           1.000000         3.000000  \n", "50%           3.000000         5.000000  \n", "75%           5.000000        10.000000  \n", "max          69.250000       107.500000  "]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}], "source": ["high_frequency_query_df=top_query_labeled_df[top_query_labeled_df[\"搜索频次标签\"]=='高频搜索词(>230)']\n", "high_frequency_query_df=high_frequency_query_df.sort_values(by=[\"searched_users\",\"searched_times\"], ascending=[False,False])\n", "high_frequency_query_df.to_csv(f\"./高频搜索词_{datetime.now().strftime('%Y%m%d')}.csv\",index=False)\n", "high_frequency_query_df.describe()"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["0.10     0.00\n", "0.25     0.00\n", "0.50     1.00\n", "0.75     2.00\n", "0.90     4.00\n", "0.95     6.00\n", "0.99    18.48\n", "Name: p50_click_index, dtype: float64\n", "0.10     0.00\n", "0.25     1.00\n", "0.50     3.00\n", "0.75     5.00\n", "0.90    10.10\n", "0.95    17.60\n", "0.99    33.68\n", "Name: p75_click_index, dtype: float64\n"]}], "source": ["print(\n", "    high_frequency_query_df[\"p50_click_index\"].quantile(\n", "        [0.1, 0.25, 0.5, 0.75, 0.9, 0.95, 0.99]\n", "    )\n", ")\n", "\n", "print(\n", "    high_frequency_query_df[\"p75_click_index\"].quantile(\n", "        [0.1, 0.25, 0.5, 0.75, 0.9, 0.95, 0.99]\n", "    )\n", ")"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>query</th>\n", "      <th>searched_users</th>\n", "      <th>searched_times</th>\n", "      <th>impression_cnt</th>\n", "      <th>click_cnt</th>\n", "      <th>sku_ctr</th>\n", "      <th>clicked_user_cnt</th>\n", "      <th>user_ctr</th>\n", "      <th>p50_click_index</th>\n", "      <th>p75_click_index</th>\n", "      <th>p90_click_index</th>\n", "      <th>搜索频次标签</th>\n", "      <th>link</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>9835</th>\n", "      <td>慕斯</td>\n", "      <td>233</td>\n", "      <td>256</td>\n", "      <td>11908</td>\n", "      <td>410</td>\n", "      <td>0.034</td>\n", "      <td>133</td>\n", "      <td>0.57</td>\n", "      <td>35.0</td>\n", "      <td>69.0</td>\n", "      <td>105.0</td>\n", "      <td>高频搜索词(&gt;230)</td>\n", "      <td>http://xianmuai.s7.tunnelfrp.com/search-arena/?query=慕斯&amp;page_size=40&amp;city=杭州</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10862</th>\n", "      <td>明治</td>\n", "      <td>193</td>\n", "      <td>263</td>\n", "      <td>1339</td>\n", "      <td>22</td>\n", "      <td>0.016</td>\n", "      <td>26</td>\n", "      <td>0.13</td>\n", "      <td>12.0</td>\n", "      <td>19.0</td>\n", "      <td>37.0</td>\n", "      <td>高频搜索词(&gt;230)</td>\n", "      <td>http://xianmuai.s7.tunnelfrp.com/search-arena/?query=明治&amp;page_size=40&amp;city=杭州</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11517</th>\n", "      <td>果酱</td>\n", "      <td>509</td>\n", "      <td>593</td>\n", "      <td>16178</td>\n", "      <td>531</td>\n", "      <td>0.033</td>\n", "      <td>256</td>\n", "      <td>0.50</td>\n", "      <td>16.0</td>\n", "      <td>29.0</td>\n", "      <td>52.0</td>\n", "      <td>高频搜索词(&gt;230)</td>\n", "      <td>http://xianmuai.s7.tunnelfrp.com/search-arena/?query=果酱&amp;page_size=40&amp;city=杭州</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13065</th>\n", "      <td>水果</td>\n", "      <td>391</td>\n", "      <td>423</td>\n", "      <td>5068</td>\n", "      <td>98</td>\n", "      <td>0.019</td>\n", "      <td>78</td>\n", "      <td>0.20</td>\n", "      <td>18.0</td>\n", "      <td>39.0</td>\n", "      <td>85.0</td>\n", "      <td>高频搜索词(&gt;230)</td>\n", "      <td>http://xianmuai.s7.tunnelfrp.com/search-arena/?query=水果&amp;page_size=40&amp;city=杭州</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13365</th>\n", "      <td>油</td>\n", "      <td>278</td>\n", "      <td>240</td>\n", "      <td>3295</td>\n", "      <td>28</td>\n", "      <td>0.008</td>\n", "      <td>25</td>\n", "      <td>0.09</td>\n", "      <td>38.0</td>\n", "      <td>61.0</td>\n", "      <td>78.0</td>\n", "      <td>高频搜索词(&gt;230)</td>\n", "      <td>http://xianmuai.s7.tunnelfrp.com/search-arena/?query=油&amp;page_size=40&amp;city=杭州</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14228</th>\n", "      <td>澄善</td>\n", "      <td>166</td>\n", "      <td>340</td>\n", "      <td>9636</td>\n", "      <td>401</td>\n", "      <td>0.042</td>\n", "      <td>107</td>\n", "      <td>0.64</td>\n", "      <td>20.0</td>\n", "      <td>32.0</td>\n", "      <td>55.0</td>\n", "      <td>高频搜索词(&gt;230)</td>\n", "      <td>http://xianmuai.s7.tunnelfrp.com/search-arena/?query=澄善&amp;page_size=40&amp;city=杭州</td>\n", "    </tr>\n", "    <tr>\n", "      <th>21037</th>\n", "      <td>蛋糕</td>\n", "      <td>613</td>\n", "      <td>1015</td>\n", "      <td>43069</td>\n", "      <td>2074</td>\n", "      <td>0.048</td>\n", "      <td>450</td>\n", "      <td>0.73</td>\n", "      <td>22.0</td>\n", "      <td>46.0</td>\n", "      <td>108.0</td>\n", "      <td>高频搜索词(&gt;230)</td>\n", "      <td>http://xianmuai.s7.tunnelfrp.com/search-arena/?query=蛋糕&amp;page_size=40&amp;city=杭州</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["      query  searched_users  searched_times  impression_cnt  click_cnt  \\\n", "9835     慕斯             233             256           11908        410   \n", "10862    明治             193             263            1339         22   \n", "11517    果酱             509             593           16178        531   \n", "13065    水果             391             423            5068         98   \n", "13365     油             278             240            3295         28   \n", "14228    澄善             166             340            9636        401   \n", "21037    蛋糕             613            1015           43069       2074   \n", "\n", "       sku_ctr  clicked_user_cnt  user_ctr  p50_click_index  p75_click_index  \\\n", "9835     0.034               133      0.57             35.0             69.0   \n", "10862    0.016                26      0.13             12.0             19.0   \n", "11517    0.033               256      0.50             16.0             29.0   \n", "13065    0.019                78      0.20             18.0             39.0   \n", "13365    0.008                25      0.09             38.0             61.0   \n", "14228    0.042               107      0.64             20.0             32.0   \n", "21037    0.048               450      0.73             22.0             46.0   \n", "\n", "       p90_click_index       搜索频次标签  \\\n", "9835             105.0  高频搜索词(>230)   \n", "10862             37.0  高频搜索词(>230)   \n", "11517             52.0  高频搜索词(>230)   \n", "13065             85.0  高频搜索词(>230)   \n", "13365             78.0  高频搜索词(>230)   \n", "14228             55.0  高频搜索词(>230)   \n", "21037            108.0  高频搜索词(>230)   \n", "\n", "                                                                               link  \n", "9835   http://xianmuai.s7.tunnelfrp.com/search-arena/?query=慕斯&page_size=40&city=杭州  \n", "10862  http://xianmuai.s7.tunnelfrp.com/search-arena/?query=明治&page_size=40&city=杭州  \n", "11517  http://xianmuai.s7.tunnelfrp.com/search-arena/?query=果酱&page_size=40&city=杭州  \n", "13065  http://xianmuai.s7.tunnelfrp.com/search-arena/?query=水果&page_size=40&city=杭州  \n", "13365   http://xianmuai.s7.tunnelfrp.com/search-arena/?query=油&page_size=40&city=杭州  \n", "14228  http://xianmuai.s7.tunnelfrp.com/search-arena/?query=澄善&page_size=40&city=杭州  \n", "21037  http://xianmuai.s7.tunnelfrp.com/search-arena/?query=蛋糕&page_size=40&city=杭州  "]}, "execution_count": 17, "metadata": {}, "output_type": "execute_result"}], "source": ["# 筛选出p50点击索引大于等于11的query\n", "poor_query_df = high_frequency_query_df[\n", "    high_frequency_query_df[\"p50_click_index\"] >= 11\n", "].copy()\n", "\n", "# 对poor_query_df的几列进行四舍五入操作，并直接在原数据上更新\n", "# 使用.loc[:, cols] 来避免 SettingWithCopyWarning\n", "cols_to_round = [\"sku_ctr\", \"user_ctr\", \"p75_click_index\", \"p90_click_index\"]\n", "rounding_levels = [3, 2, 0, 0]  # 为每列指定不同的舍入精度\n", "\n", "for col, level in zip(cols_to_round, rounding_levels):\n", "    poor_query_df.loc[:, col] = poor_query_df[col].round(level)\n", "poor_query_df[\"link\"]=poor_query_df['query'].apply(lambda x: f\"http://xianmuai.s7.tunnelfrp.com/search-arena/?query={x}&page_size=40&city=杭州\")\n", "poor_query_df"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>query</th>\n", "      <th>searched_users</th>\n", "      <th>searched_times</th>\n", "      <th>impression_cnt</th>\n", "      <th>click_cnt</th>\n", "      <th>sku_ctr</th>\n", "      <th>clicked_user_cnt</th>\n", "      <th>user_ctr</th>\n", "      <th>p50_click_index</th>\n", "      <th>p75_click_index</th>\n", "      <th>p90_click_index</th>\n", "      <th>搜索频次标签</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>9134</th>\n", "      <td>慕斯</td>\n", "      <td>610</td>\n", "      <td>864</td>\n", "      <td>35807</td>\n", "      <td>1083</td>\n", "      <td>0.030245</td>\n", "      <td>365</td>\n", "      <td>0.598361</td>\n", "      <td>26.0</td>\n", "      <td>51.00</td>\n", "      <td>91.0</td>\n", "      <td>高频搜索词(&gt;230)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10685</th>\n", "      <td>果酱</td>\n", "      <td>637</td>\n", "      <td>838</td>\n", "      <td>28166</td>\n", "      <td>796</td>\n", "      <td>0.028261</td>\n", "      <td>344</td>\n", "      <td>0.540031</td>\n", "      <td>18.0</td>\n", "      <td>36.00</td>\n", "      <td>58.0</td>\n", "      <td>高频搜索词(&gt;230)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11418</th>\n", "      <td>椰奶</td>\n", "      <td>335</td>\n", "      <td>359</td>\n", "      <td>7074</td>\n", "      <td>110</td>\n", "      <td>0.015550</td>\n", "      <td>71</td>\n", "      <td>0.211940</td>\n", "      <td>17.5</td>\n", "      <td>44.75</td>\n", "      <td>51.1</td>\n", "      <td>高频搜索词(&gt;230)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12136</th>\n", "      <td>水果</td>\n", "      <td>382</td>\n", "      <td>390</td>\n", "      <td>6998</td>\n", "      <td>145</td>\n", "      <td>0.020720</td>\n", "      <td>76</td>\n", "      <td>0.198953</td>\n", "      <td>17.0</td>\n", "      <td>64.00</td>\n", "      <td>114.8</td>\n", "      <td>高频搜索词(&gt;230)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14355</th>\n", "      <td>玉米淀粉</td>\n", "      <td>368</td>\n", "      <td>465</td>\n", "      <td>7873</td>\n", "      <td>143</td>\n", "      <td>0.018163</td>\n", "      <td>92</td>\n", "      <td>0.250000</td>\n", "      <td>18.0</td>\n", "      <td>28.00</td>\n", "      <td>39.8</td>\n", "      <td>高频搜索词(&gt;230)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>19583</th>\n", "      <td>蛋糕</td>\n", "      <td>1197</td>\n", "      <td>2360</td>\n", "      <td>123035</td>\n", "      <td>4986</td>\n", "      <td>0.040525</td>\n", "      <td>933</td>\n", "      <td>0.779449</td>\n", "      <td>23.0</td>\n", "      <td>60.00</td>\n", "      <td>123.0</td>\n", "      <td>高频搜索词(&gt;230)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>22291</th>\n", "      <td>香肠</td>\n", "      <td>203</td>\n", "      <td>299</td>\n", "      <td>7951</td>\n", "      <td>383</td>\n", "      <td>0.048170</td>\n", "      <td>136</td>\n", "      <td>0.669951</td>\n", "      <td>17.0</td>\n", "      <td>34.00</td>\n", "      <td>53.9</td>\n", "      <td>高频搜索词(&gt;230)</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["      query  searched_users  searched_times  impression_cnt  click_cnt  \\\n", "9134     慕斯             610             864           35807       1083   \n", "10685    果酱             637             838           28166        796   \n", "11418    椰奶             335             359            7074        110   \n", "12136    水果             382             390            6998        145   \n", "14355  玉米淀粉             368             465            7873        143   \n", "19583    蛋糕            1197            2360          123035       4986   \n", "22291    香肠             203             299            7951        383   \n", "\n", "        sku_ctr  clicked_user_cnt  user_ctr  p50_click_index  p75_click_index  \\\n", "9134   0.030245               365  0.598361             26.0            51.00   \n", "10685  0.028261               344  0.540031             18.0            36.00   \n", "11418  0.015550                71  0.211940             17.5            44.75   \n", "12136  0.020720                76  0.198953             17.0            64.00   \n", "14355  0.018163                92  0.250000             18.0            28.00   \n", "19583  0.040525               933  0.779449             23.0            60.00   \n", "22291  0.048170               136  0.669951             17.0            34.00   \n", "\n", "       p90_click_index       搜索频次标签  \n", "9134              91.0  高频搜索词(>230)  \n", "10685             58.0  高频搜索词(>230)  \n", "11418             51.1  高频搜索词(>230)  \n", "12136            114.8  高频搜索词(>230)  \n", "14355             39.8  高频搜索词(>230)  \n", "19583            123.0  高频搜索词(>230)  \n", "22291             53.9  高频搜索词(>230)  "]}, "execution_count": 16, "metadata": {}, "output_type": "execute_result"}], "source": ["poor_high_frequency_query_df = high_frequency_query_df[\n", "    (high_frequency_query_df[\"p50_click_index\"] >= 17.0)\n", "    | (poor_query_df[\"p75_click_index\"] > 35)\n", "]\n", "\n", "poor_high_frequency_query_df"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>query</th>\n", "      <th>searched_users</th>\n", "      <th>searched_times</th>\n", "      <th>impression_cnt</th>\n", "      <th>click_cnt</th>\n", "      <th>sku_ctr</th>\n", "      <th>clicked_user_cnt</th>\n", "      <th>user_ctr</th>\n", "      <th>p50_click_index</th>\n", "      <th>p75_click_index</th>\n", "      <th>p90_click_index</th>\n", "      <th>搜索频次标签</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>15603</th>\n", "      <td>碰射奶油</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>271</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>低频搜索词(&lt;10)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16245</th>\n", "      <td>紫薯泥</td>\n", "      <td>4</td>\n", "      <td>8</td>\n", "      <td>213</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>低频搜索词(&lt;10)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3218</th>\n", "      <td>冷冻西点</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>206</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>低频搜索词(&lt;10)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14739</th>\n", "      <td>甘草酸梅酱</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>197</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>低频搜索词(&lt;10)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6696</th>\n", "      <td>娃哈哈钙奶</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>184</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>低频搜索词(&lt;10)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9898</th>\n", "      <td>无水奶油</td>\n", "      <td>6</td>\n", "      <td>8</td>\n", "      <td>171</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>低频搜索词(&lt;10)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9104</th>\n", "      <td>意文</td>\n", "      <td>8</td>\n", "      <td>6</td>\n", "      <td>166</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>低频搜索词(&lt;10)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10336</th>\n", "      <td>木棉花白砂糖</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>161</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>低频搜索词(&lt;10)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10598</th>\n", "      <td>果汁感</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>160</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>低频搜索词(&lt;10)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>19507</th>\n", "      <td>虎皮卷</td>\n", "      <td>5</td>\n", "      <td>6</td>\n", "      <td>157</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>低频搜索词(&lt;10)</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["        query  searched_users  searched_times  impression_cnt  click_cnt  \\\n", "15603    碰射奶油               1               1             271          0   \n", "16245     紫薯泥               4               8             213          0   \n", "3218     冷冻西点               1               1             206          0   \n", "14739   甘草酸梅酱               1               1             197          0   \n", "6696    娃哈哈钙奶               1               1             184          0   \n", "9898     无水奶油               6               8             171          0   \n", "9104       意文               8               6             166          0   \n", "10336  木棉花白砂糖               1               1             161          0   \n", "10598     果汁感               1               1             160          0   \n", "19507     虎皮卷               5               6             157          0   \n", "\n", "       sku_ctr  clicked_user_cnt  user_ctr  p50_click_index  p75_click_index  \\\n", "15603      0.0                 0       0.0              NaN              NaN   \n", "16245      0.0                 0       0.0              NaN              NaN   \n", "3218       0.0                 0       0.0              NaN              NaN   \n", "14739      0.0                 0       0.0              NaN              NaN   \n", "6696       0.0                 0       0.0              NaN              NaN   \n", "9898       0.0                 0       0.0              NaN              NaN   \n", "9104       0.0                 0       0.0              NaN              NaN   \n", "10336      0.0                 0       0.0              NaN              NaN   \n", "10598      0.0                 0       0.0              NaN              NaN   \n", "19507      0.0                 0       0.0              NaN              NaN   \n", "\n", "       p90_click_index      搜索频次标签  \n", "15603              NaN  低频搜索词(<10)  \n", "16245              NaN  低频搜索词(<10)  \n", "3218               NaN  低频搜索词(<10)  \n", "14739              NaN  低频搜索词(<10)  \n", "6696               NaN  低频搜索词(<10)  \n", "9898               NaN  低频搜索词(<10)  \n", "9104               NaN  低频搜索词(<10)  \n", "10336              NaN  低频搜索词(<10)  \n", "10598              NaN  低频搜索词(<10)  \n", "19507              NaN  低频搜索词(<10)  "]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>query</th>\n", "      <th>searched_users</th>\n", "      <th>searched_times</th>\n", "      <th>impression_cnt</th>\n", "      <th>click_cnt</th>\n", "      <th>sku_ctr</th>\n", "      <th>clicked_user_cnt</th>\n", "      <th>user_ctr</th>\n", "      <th>p50_click_index</th>\n", "      <th>p75_click_index</th>\n", "      <th>p90_click_index</th>\n", "      <th>搜索频次标签</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>21922</th>\n", "      <td>面包</td>\n", "      <td>202</td>\n", "      <td>200</td>\n", "      <td>4833</td>\n", "      <td>74</td>\n", "      <td>0.015311</td>\n", "      <td>51</td>\n", "      <td>0.252475</td>\n", "      <td>21.5</td>\n", "      <td>28.00</td>\n", "      <td>45.4</td>\n", "      <td>中频搜索词</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4887</th>\n", "      <td>咸蛋黄</td>\n", "      <td>135</td>\n", "      <td>143</td>\n", "      <td>2013</td>\n", "      <td>35</td>\n", "      <td>0.017387</td>\n", "      <td>26</td>\n", "      <td>0.192593</td>\n", "      <td>10.0</td>\n", "      <td>17.00</td>\n", "      <td>43.0</td>\n", "      <td>中频搜索词</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7666</th>\n", "      <td>寒天</td>\n", "      <td>193</td>\n", "      <td>223</td>\n", "      <td>1981</td>\n", "      <td>40</td>\n", "      <td>0.020192</td>\n", "      <td>35</td>\n", "      <td>0.181347</td>\n", "      <td>9.0</td>\n", "      <td>11.00</td>\n", "      <td>23.5</td>\n", "      <td>中频搜索词</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14755</th>\n", "      <td>甜品</td>\n", "      <td>106</td>\n", "      <td>116</td>\n", "      <td>1912</td>\n", "      <td>32</td>\n", "      <td>0.016736</td>\n", "      <td>22</td>\n", "      <td>0.207547</td>\n", "      <td>10.0</td>\n", "      <td>17.00</td>\n", "      <td>26.5</td>\n", "      <td>中频搜索词</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6120</th>\n", "      <td>奶咖</td>\n", "      <td>72</td>\n", "      <td>111</td>\n", "      <td>1896</td>\n", "      <td>37</td>\n", "      <td>0.019515</td>\n", "      <td>23</td>\n", "      <td>0.319444</td>\n", "      <td>3.0</td>\n", "      <td>7.00</td>\n", "      <td>23.8</td>\n", "      <td>中频搜索词</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14352</th>\n", "      <td>玉米油</td>\n", "      <td>133</td>\n", "      <td>143</td>\n", "      <td>1531</td>\n", "      <td>15</td>\n", "      <td>0.009798</td>\n", "      <td>11</td>\n", "      <td>0.082707</td>\n", "      <td>9.0</td>\n", "      <td>10.00</td>\n", "      <td>14.6</td>\n", "      <td>中频搜索词</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13798</th>\n", "      <td>爱心慕斯</td>\n", "      <td>54</td>\n", "      <td>47</td>\n", "      <td>1450</td>\n", "      <td>22</td>\n", "      <td>0.015172</td>\n", "      <td>12</td>\n", "      <td>0.222222</td>\n", "      <td>51.5</td>\n", "      <td>128.50</td>\n", "      <td>153.0</td>\n", "      <td>中频搜索词</td>\n", "    </tr>\n", "    <tr>\n", "      <th>20802</th>\n", "      <td>酥油</td>\n", "      <td>65</td>\n", "      <td>73</td>\n", "      <td>1447</td>\n", "      <td>14</td>\n", "      <td>0.009675</td>\n", "      <td>14</td>\n", "      <td>0.215385</td>\n", "      <td>4.5</td>\n", "      <td>15.00</td>\n", "      <td>32.6</td>\n", "      <td>中频搜索词</td>\n", "    </tr>\n", "    <tr>\n", "      <th>20873</th>\n", "      <td>酸奶油</td>\n", "      <td>62</td>\n", "      <td>76</td>\n", "      <td>1348</td>\n", "      <td>22</td>\n", "      <td>0.016320</td>\n", "      <td>19</td>\n", "      <td>0.306452</td>\n", "      <td>10.0</td>\n", "      <td>23.75</td>\n", "      <td>35.3</td>\n", "      <td>中频搜索词</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10514</th>\n", "      <td>杯子</td>\n", "      <td>67</td>\n", "      <td>58</td>\n", "      <td>1207</td>\n", "      <td>9</td>\n", "      <td>0.007457</td>\n", "      <td>8</td>\n", "      <td>0.119403</td>\n", "      <td>22.0</td>\n", "      <td>28.00</td>\n", "      <td>35.2</td>\n", "      <td>中频搜索词</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["      query  searched_users  searched_times  impression_cnt  click_cnt  \\\n", "21922    面包             202             200            4833         74   \n", "4887    咸蛋黄             135             143            2013         35   \n", "7666     寒天             193             223            1981         40   \n", "14755    甜品             106             116            1912         32   \n", "6120     奶咖              72             111            1896         37   \n", "14352   玉米油             133             143            1531         15   \n", "13798  爱心慕斯              54              47            1450         22   \n", "20802    酥油              65              73            1447         14   \n", "20873   酸奶油              62              76            1348         22   \n", "10514    杯子              67              58            1207          9   \n", "\n", "        sku_ctr  clicked_user_cnt  user_ctr  p50_click_index  p75_click_index  \\\n", "21922  0.015311                51  0.252475             21.5            28.00   \n", "4887   0.017387                26  0.192593             10.0            17.00   \n", "7666   0.020192                35  0.181347              9.0            11.00   \n", "14755  0.016736                22  0.207547             10.0            17.00   \n", "6120   0.019515                23  0.319444              3.0             7.00   \n", "14352  0.009798                11  0.082707              9.0            10.00   \n", "13798  0.015172                12  0.222222             51.5           128.50   \n", "20802  0.009675                14  0.215385              4.5            15.00   \n", "20873  0.016320                19  0.306452             10.0            23.75   \n", "10514  0.007457                 8  0.119403             22.0            28.00   \n", "\n", "       p90_click_index 搜索频次标签  \n", "21922             45.4  中频搜索词  \n", "4887              43.0  中频搜索词  \n", "7666              23.5  中频搜索词  \n", "14755             26.5  中频搜索词  \n", "6120              23.8  中频搜索词  \n", "14352             14.6  中频搜索词  \n", "13798            153.0  中频搜索词  \n", "20802             32.6  中频搜索词  \n", "20873             35.3  中频搜索词  \n", "10514             35.2  中频搜索词  "]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>query</th>\n", "      <th>searched_users</th>\n", "      <th>searched_times</th>\n", "      <th>impression_cnt</th>\n", "      <th>click_cnt</th>\n", "      <th>sku_ctr</th>\n", "      <th>clicked_user_cnt</th>\n", "      <th>user_ctr</th>\n", "      <th>p50_click_index</th>\n", "      <th>p75_click_index</th>\n", "      <th>p90_click_index</th>\n", "      <th>搜索频次标签</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>6139</th>\n", "      <td>奶油</td>\n", "      <td>5211</td>\n", "      <td>9543</td>\n", "      <td>262185</td>\n", "      <td>10141</td>\n", "      <td>0.038679</td>\n", "      <td>3574</td>\n", "      <td>0.685857</td>\n", "      <td>12.0</td>\n", "      <td>30.0</td>\n", "      <td>50.0</td>\n", "      <td>高频搜索词(&gt;230)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>23226</th>\n", "      <td>黄油</td>\n", "      <td>4608</td>\n", "      <td>8282</td>\n", "      <td>179807</td>\n", "      <td>6590</td>\n", "      <td>0.036650</td>\n", "      <td>2813</td>\n", "      <td>0.610593</td>\n", "      <td>10.0</td>\n", "      <td>22.0</td>\n", "      <td>31.0</td>\n", "      <td>高频搜索词(&gt;230)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12968</th>\n", "      <td>淡奶油</td>\n", "      <td>2942</td>\n", "      <td>5705</td>\n", "      <td>137980</td>\n", "      <td>6176</td>\n", "      <td>0.044760</td>\n", "      <td>2250</td>\n", "      <td>0.764786</td>\n", "      <td>9.0</td>\n", "      <td>24.0</td>\n", "      <td>45.0</td>\n", "      <td>高频搜索词(&gt;230)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>19583</th>\n", "      <td>蛋糕</td>\n", "      <td>1197</td>\n", "      <td>2360</td>\n", "      <td>123035</td>\n", "      <td>4986</td>\n", "      <td>0.040525</td>\n", "      <td>933</td>\n", "      <td>0.779449</td>\n", "      <td>23.0</td>\n", "      <td>60.0</td>\n", "      <td>123.0</td>\n", "      <td>高频搜索词(&gt;230)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8046</th>\n", "      <td>巧克力</td>\n", "      <td>1442</td>\n", "      <td>2011</td>\n", "      <td>44069</td>\n", "      <td>1457</td>\n", "      <td>0.033062</td>\n", "      <td>734</td>\n", "      <td>0.509015</td>\n", "      <td>11.0</td>\n", "      <td>22.0</td>\n", "      <td>41.0</td>\n", "      <td>高频搜索词(&gt;230)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>19017</th>\n", "      <td>葡萄</td>\n", "      <td>1228</td>\n", "      <td>3107</td>\n", "      <td>44050</td>\n", "      <td>1661</td>\n", "      <td>0.037707</td>\n", "      <td>628</td>\n", "      <td>0.512653</td>\n", "      <td>5.0</td>\n", "      <td>11.0</td>\n", "      <td>23.0</td>\n", "      <td>高频搜索词(&gt;230)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>17955</th>\n", "      <td>芝士</td>\n", "      <td>1531</td>\n", "      <td>2086</td>\n", "      <td>43332</td>\n", "      <td>1588</td>\n", "      <td>0.036647</td>\n", "      <td>800</td>\n", "      <td>0.522534</td>\n", "      <td>10.0</td>\n", "      <td>22.0</td>\n", "      <td>38.0</td>\n", "      <td>高频搜索词(&gt;230)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9134</th>\n", "      <td>慕斯</td>\n", "      <td>610</td>\n", "      <td>864</td>\n", "      <td>35807</td>\n", "      <td>1083</td>\n", "      <td>0.030245</td>\n", "      <td>365</td>\n", "      <td>0.598361</td>\n", "      <td>26.0</td>\n", "      <td>51.0</td>\n", "      <td>91.0</td>\n", "      <td>高频搜索词(&gt;230)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4273</th>\n", "      <td>可可粉</td>\n", "      <td>1085</td>\n", "      <td>1800</td>\n", "      <td>31311</td>\n", "      <td>1183</td>\n", "      <td>0.037782</td>\n", "      <td>621</td>\n", "      <td>0.572350</td>\n", "      <td>6.0</td>\n", "      <td>17.0</td>\n", "      <td>28.0</td>\n", "      <td>高频搜索词(&gt;230)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7306</th>\n", "      <td>安德鲁</td>\n", "      <td>821</td>\n", "      <td>1315</td>\n", "      <td>30238</td>\n", "      <td>1190</td>\n", "      <td>0.039354</td>\n", "      <td>523</td>\n", "      <td>0.637028</td>\n", "      <td>14.0</td>\n", "      <td>22.0</td>\n", "      <td>33.0</td>\n", "      <td>高频搜索词(&gt;230)</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["      query  searched_users  searched_times  impression_cnt  click_cnt  \\\n", "6139     奶油            5211            9543          262185      10141   \n", "23226    黄油            4608            8282          179807       6590   \n", "12968   淡奶油            2942            5705          137980       6176   \n", "19583    蛋糕            1197            2360          123035       4986   \n", "8046    巧克力            1442            2011           44069       1457   \n", "19017    葡萄            1228            3107           44050       1661   \n", "17955    芝士            1531            2086           43332       1588   \n", "9134     慕斯             610             864           35807       1083   \n", "4273    可可粉            1085            1800           31311       1183   \n", "7306    安德鲁             821            1315           30238       1190   \n", "\n", "        sku_ctr  clicked_user_cnt  user_ctr  p50_click_index  p75_click_index  \\\n", "6139   0.038679              3574  0.685857             12.0             30.0   \n", "23226  0.036650              2813  0.610593             10.0             22.0   \n", "12968  0.044760              2250  0.764786              9.0             24.0   \n", "19583  0.040525               933  0.779449             23.0             60.0   \n", "8046   0.033062               734  0.509015             11.0             22.0   \n", "19017  0.037707               628  0.512653              5.0             11.0   \n", "17955  0.036647               800  0.522534             10.0             22.0   \n", "9134   0.030245               365  0.598361             26.0             51.0   \n", "4273   0.037782               621  0.572350              6.0             17.0   \n", "7306   0.039354               523  0.637028             14.0             22.0   \n", "\n", "       p90_click_index       搜索频次标签  \n", "6139              50.0  高频搜索词(>230)  \n", "23226             31.0  高频搜索词(>230)  \n", "12968             45.0  高频搜索词(>230)  \n", "19583            123.0  高频搜索词(>230)  \n", "8046              41.0  高频搜索词(>230)  \n", "19017             23.0  高频搜索词(>230)  \n", "17955             38.0  高频搜索词(>230)  \n", "9134              91.0  高频搜索词(>230)  \n", "4273              28.0  高频搜索词(>230)  \n", "7306              33.0  高频搜索词(>230)  "]}, "metadata": {}, "output_type": "display_data"}], "source": ["# 为每个标签创建一个字典来存储对应的DataFrame\n", "dfs_by_label = {}\n", "\n", "# 获取所有唯一的搜索频次标签\n", "unique_labels = top_query_labeled_df[\"搜索频次标签\"].unique()\n", "\n", "for label in unique_labels:\n", "    # 计算当前标签下user_ctr的10%分位数\n", "    label_df = top_query_labeled_df[top_query_labeled_df[\"搜索频次标签\"] == label]\n", "    label_df.to_csv(f\"商城搜索词列表_{label}.csv\", index=False)\n", "    quantile_10 = label_df[\"sku_ctr\"].quantile(0.1)\n", "    # 筛选出当前标签下sku_ctr小于等于10%分位数的数据\n", "    df_filtered = label_df[(label_df[\"sku_ctr\"] <= quantile_10)]\n", "    # 将筛选后的DataFrame存储到字典中，键为标签名\n", "    dfs_by_label[label] = df_filtered\n", "\n", "# 现在dfs_by_label包含了每个标签对应的DataFrame，你可以通过标签名访问它们\n", "# 例如, 要访问高频搜索词的DataFrame:\n", "high_frequency_df = dfs_by_label[\"高频搜索词(>230)\"]\n", "low_perf_df = high_frequency_df[high_frequency_df[\"p50_click_index\"] > 5]\n", "low_perf_df\n", "# 要访问所有标签的df，可以这样遍历\n", "for label, df in dfs_by_label.items():\n", "    df = df.sort_values(by=\"impression_cnt\", ascending=False)\n", "    display(df.head(10))"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>searched_users</th>\n", "      <th>searched_times</th>\n", "      <th>impression_cnt</th>\n", "      <th>click_cnt</th>\n", "      <th>sku_ctr</th>\n", "      <th>clicked_user_cnt</th>\n", "      <th>user_ctr</th>\n", "      <th>p50_click_index</th>\n", "      <th>p75_click_index</th>\n", "      <th>p90_click_index</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>count</th>\n", "      <td>2792.000000</td>\n", "      <td>2792.000000</td>\n", "      <td>2792.000000</td>\n", "      <td>2792.000000</td>\n", "      <td>2792.000000</td>\n", "      <td>2792.000000</td>\n", "      <td>2792.000000</td>\n", "      <td>2709.000000</td>\n", "      <td>2709.000000</td>\n", "      <td>2709.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>mean</th>\n", "      <td>33.585602</td>\n", "      <td>44.300143</td>\n", "      <td>406.232450</td>\n", "      <td>34.861748</td>\n", "      <td>0.100182</td>\n", "      <td>18.438037</td>\n", "      <td>0.532095</td>\n", "      <td>3.995386</td>\n", "      <td>7.312846</td>\n", "      <td>12.239535</td>\n", "    </tr>\n", "    <tr>\n", "      <th>std</th>\n", "      <td>32.849687</td>\n", "      <td>45.390801</td>\n", "      <td>489.001102</td>\n", "      <td>43.734929</td>\n", "      <td>0.102139</td>\n", "      <td>21.466457</td>\n", "      <td>0.250782</td>\n", "      <td>7.425558</td>\n", "      <td>11.080501</td>\n", "      <td>17.077813</td>\n", "    </tr>\n", "    <tr>\n", "      <th>min</th>\n", "      <td>1.000000</td>\n", "      <td>10.000000</td>\n", "      <td>13.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>25%</th>\n", "      <td>12.000000</td>\n", "      <td>14.000000</td>\n", "      <td>121.000000</td>\n", "      <td>8.000000</td>\n", "      <td>0.040179</td>\n", "      <td>5.000000</td>\n", "      <td>0.352941</td>\n", "      <td>0.000000</td>\n", "      <td>1.000000</td>\n", "      <td>2.600000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>50%</th>\n", "      <td>21.000000</td>\n", "      <td>25.000000</td>\n", "      <td>228.000000</td>\n", "      <td>18.000000</td>\n", "      <td>0.083333</td>\n", "      <td>10.000000</td>\n", "      <td>0.563605</td>\n", "      <td>1.000000</td>\n", "      <td>3.000000</td>\n", "      <td>7.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>75%</th>\n", "      <td>43.000000</td>\n", "      <td>56.000000</td>\n", "      <td>505.000000</td>\n", "      <td>43.000000</td>\n", "      <td>0.139040</td>\n", "      <td>23.000000</td>\n", "      <td>0.729567</td>\n", "      <td>4.000000</td>\n", "      <td>9.000000</td>\n", "      <td>16.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>max</th>\n", "      <td>220.000000</td>\n", "      <td>230.000000</td>\n", "      <td>5701.000000</td>\n", "      <td>335.000000</td>\n", "      <td>3.240000</td>\n", "      <td>155.000000</td>\n", "      <td>1.000000</td>\n", "      <td>91.000000</td>\n", "      <td>167.000000</td>\n", "      <td>264.200000</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["       searched_users  searched_times  impression_cnt    click_cnt  \\\n", "count     2792.000000     2792.000000     2792.000000  2792.000000   \n", "mean        33.585602       44.300143      406.232450    34.861748   \n", "std         32.849687       45.390801      489.001102    43.734929   \n", "min          1.000000       10.000000       13.000000     0.000000   \n", "25%         12.000000       14.000000      121.000000     8.000000   \n", "50%         21.000000       25.000000      228.000000    18.000000   \n", "75%         43.000000       56.000000      505.000000    43.000000   \n", "max        220.000000      230.000000     5701.000000   335.000000   \n", "\n", "           sku_ctr  clicked_user_cnt     user_ctr  p50_click_index  \\\n", "count  2792.000000       2792.000000  2792.000000      2709.000000   \n", "mean      0.100182         18.438037     0.532095         3.995386   \n", "std       0.102139         21.466457     0.250782         7.425558   \n", "min       0.000000          0.000000     0.000000         0.000000   \n", "25%       0.040179          5.000000     0.352941         0.000000   \n", "50%       0.083333         10.000000     0.563605         1.000000   \n", "75%       0.139040         23.000000     0.729567         4.000000   \n", "max       3.240000        155.000000     1.000000        91.000000   \n", "\n", "       p75_click_index  p90_click_index  \n", "count      2709.000000      2709.000000  \n", "mean          7.312846        12.239535  \n", "std          11.080501        17.077813  \n", "min           0.000000         0.000000  \n", "25%           1.000000         2.600000  \n", "50%           3.000000         7.000000  \n", "75%           9.000000        16.000000  \n", "max         167.000000       264.200000  "]}, "execution_count": 18, "metadata": {}, "output_type": "execute_result"}], "source": ["medium_df = top_query_labeled_df[top_query_labeled_df[\"搜索频次标签\"] == \"中频搜索词\"]\n", "medium_df.describe()"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["       searched_users  searched_times  impression_cnt  click_cnt    sku_ctr  \\\n", "count       88.000000       88.000000       88.000000  88.000000  88.000000   \n", "mean        18.318182       21.079545      176.318182   1.340909   0.005817   \n", "std         21.634660       22.227260      186.876353   2.651767   0.007330   \n", "min          1.000000       10.000000       31.000000   0.000000   0.000000   \n", "25%         10.000000       12.000000       86.750000   0.000000   0.000000   \n", "50%         13.000000       16.000000      127.500000   1.000000   0.004040   \n", "75%         21.000000       22.250000      196.250000   2.000000   0.009285   \n", "max        157.000000      168.000000     1531.000000  17.000000   0.032558   \n", "\n", "       clicked_user_cnt   user_ctr  p50_click_index  p75_click_index  \\\n", "count         88.000000  88.000000        46.000000        46.000000   \n", "mean           1.045455   0.038655         8.858696         9.548913   \n", "std            2.078401   0.038784         8.711208         8.906399   \n", "min            0.000000   0.000000         0.000000         0.000000   \n", "25%            0.000000   0.000000         4.000000         4.000000   \n", "50%            1.000000   0.039596         5.000000         6.000000   \n", "75%            1.000000   0.076923        10.375000        10.937500   \n", "max           15.000000   0.096154        36.000000        36.000000   \n", "\n", "       p90_click_index  \n", "count        46.000000  \n", "mean         10.136957  \n", "std           9.233908  \n", "min           0.000000  \n", "25%           4.000000  \n", "50%           7.300000  \n", "75%          13.975000  \n", "max          36.000000  \n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>query</th>\n", "      <th>searched_users</th>\n", "      <th>searched_times</th>\n", "      <th>impression_cnt</th>\n", "      <th>click_cnt</th>\n", "      <th>sku_ctr</th>\n", "      <th>clicked_user_cnt</th>\n", "      <th>user_ctr</th>\n", "      <th>p50_click_index</th>\n", "      <th>p75_click_index</th>\n", "      <th>p90_click_index</th>\n", "      <th>搜索频次标签</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>22803</th>\n", "      <td>鲜沐</td>\n", "      <td>157</td>\n", "      <td>168</td>\n", "      <td>838</td>\n", "      <td>17</td>\n", "      <td>0.020286</td>\n", "      <td>15</td>\n", "      <td>0.096154</td>\n", "      <td>3.0</td>\n", "      <td>4.00</td>\n", "      <td>4.4</td>\n", "      <td>中频搜索词</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14352</th>\n", "      <td>玉米油</td>\n", "      <td>133</td>\n", "      <td>143</td>\n", "      <td>1531</td>\n", "      <td>15</td>\n", "      <td>0.009798</td>\n", "      <td>11</td>\n", "      <td>0.082707</td>\n", "      <td>9.0</td>\n", "      <td>10.00</td>\n", "      <td>14.6</td>\n", "      <td>中频搜索词</td>\n", "    </tr>\n", "    <tr>\n", "      <th>17584</th>\n", "      <td>色拉油</td>\n", "      <td>53</td>\n", "      <td>51</td>\n", "      <td>404</td>\n", "      <td>6</td>\n", "      <td>0.014851</td>\n", "      <td>4</td>\n", "      <td>0.075472</td>\n", "      <td>3.5</td>\n", "      <td>4.00</td>\n", "      <td>7.0</td>\n", "      <td>中频搜索词</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12706</th>\n", "      <td>泰象矿泉水</td>\n", "      <td>35</td>\n", "      <td>44</td>\n", "      <td>247</td>\n", "      <td>5</td>\n", "      <td>0.020243</td>\n", "      <td>3</td>\n", "      <td>0.085714</td>\n", "      <td>3.0</td>\n", "      <td>5.00</td>\n", "      <td>5.0</td>\n", "      <td>中频搜索词</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2586</th>\n", "      <td>保鲜膜</td>\n", "      <td>43</td>\n", "      <td>42</td>\n", "      <td>476</td>\n", "      <td>4</td>\n", "      <td>0.008403</td>\n", "      <td>4</td>\n", "      <td>0.093023</td>\n", "      <td>4.0</td>\n", "      <td>4.25</td>\n", "      <td>4.7</td>\n", "      <td>中频搜索词</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4520</th>\n", "      <td>君乐宝</td>\n", "      <td>33</td>\n", "      <td>36</td>\n", "      <td>326</td>\n", "      <td>2</td>\n", "      <td>0.006135</td>\n", "      <td>2</td>\n", "      <td>0.060606</td>\n", "      <td>4.0</td>\n", "      <td>4.00</td>\n", "      <td>4.0</td>\n", "      <td>中频搜索词</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2895</th>\n", "      <td>冰勃朗</td>\n", "      <td>35</td>\n", "      <td>34</td>\n", "      <td>349</td>\n", "      <td>3</td>\n", "      <td>0.008596</td>\n", "      <td>3</td>\n", "      <td>0.085714</td>\n", "      <td>4.0</td>\n", "      <td>7.50</td>\n", "      <td>9.6</td>\n", "      <td>中频搜索词</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10985</th>\n", "      <td>树梅</td>\n", "      <td>25</td>\n", "      <td>31</td>\n", "      <td>215</td>\n", "      <td>7</td>\n", "      <td>0.032558</td>\n", "      <td>2</td>\n", "      <td>0.080000</td>\n", "      <td>4.0</td>\n", "      <td>4.00</td>\n", "      <td>4.0</td>\n", "      <td>中频搜索词</td>\n", "    </tr>\n", "    <tr>\n", "      <th>20910</th>\n", "      <td>酸黄瓜</td>\n", "      <td>26</td>\n", "      <td>30</td>\n", "      <td>342</td>\n", "      <td>2</td>\n", "      <td>0.005848</td>\n", "      <td>2</td>\n", "      <td>0.076923</td>\n", "      <td>5.5</td>\n", "      <td>7.75</td>\n", "      <td>9.1</td>\n", "      <td>中频搜索词</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16729</th>\n", "      <td>维</td>\n", "      <td>29</td>\n", "      <td>29</td>\n", "      <td>352</td>\n", "      <td>1</td>\n", "      <td>0.002841</td>\n", "      <td>1</td>\n", "      <td>0.035714</td>\n", "      <td>4.0</td>\n", "      <td>4.00</td>\n", "      <td>4.0</td>\n", "      <td>中频搜索词</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5540</th>\n", "      <td>大多肉</td>\n", "      <td>29</td>\n", "      <td>29</td>\n", "      <td>201</td>\n", "      <td>0</td>\n", "      <td>0.000000</td>\n", "      <td>0</td>\n", "      <td>0.000000</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>中频搜索词</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6173</th>\n", "      <td>奶油卡</td>\n", "      <td>28</td>\n", "      <td>26</td>\n", "      <td>269</td>\n", "      <td>2</td>\n", "      <td>0.007435</td>\n", "      <td>2</td>\n", "      <td>0.071429</td>\n", "      <td>3.0</td>\n", "      <td>3.50</td>\n", "      <td>3.8</td>\n", "      <td>中频搜索词</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5953</th>\n", "      <td>奥利</td>\n", "      <td>29</td>\n", "      <td>26</td>\n", "      <td>225</td>\n", "      <td>1</td>\n", "      <td>0.004444</td>\n", "      <td>1</td>\n", "      <td>0.034483</td>\n", "      <td>22.0</td>\n", "      <td>22.00</td>\n", "      <td>22.0</td>\n", "      <td>中频搜索词</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12759</th>\n", "      <td>测试</td>\n", "      <td>6</td>\n", "      <td>26</td>\n", "      <td>120</td>\n", "      <td>0</td>\n", "      <td>0.000000</td>\n", "      <td>0</td>\n", "      <td>0.000000</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>中频搜索词</td>\n", "    </tr>\n", "    <tr>\n", "      <th>17653</th>\n", "      <td>艾素糖</td>\n", "      <td>25</td>\n", "      <td>25</td>\n", "      <td>304</td>\n", "      <td>2</td>\n", "      <td>0.006579</td>\n", "      <td>2</td>\n", "      <td>0.080000</td>\n", "      <td>6.0</td>\n", "      <td>7.00</td>\n", "      <td>7.6</td>\n", "      <td>中频搜索词</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12014</th>\n", "      <td>正荣</td>\n", "      <td>23</td>\n", "      <td>25</td>\n", "      <td>177</td>\n", "      <td>2</td>\n", "      <td>0.011299</td>\n", "      <td>2</td>\n", "      <td>0.086957</td>\n", "      <td>7.0</td>\n", "      <td>9.00</td>\n", "      <td>10.2</td>\n", "      <td>中频搜索词</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6741</th>\n", "      <td>季歌</td>\n", "      <td>19</td>\n", "      <td>24</td>\n", "      <td>158</td>\n", "      <td>1</td>\n", "      <td>0.006329</td>\n", "      <td>1</td>\n", "      <td>0.052632</td>\n", "      <td>0.0</td>\n", "      <td>0.00</td>\n", "      <td>0.0</td>\n", "      <td>中频搜索词</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2236</th>\n", "      <td>优</td>\n", "      <td>25</td>\n", "      <td>24</td>\n", "      <td>168</td>\n", "      <td>2</td>\n", "      <td>0.011905</td>\n", "      <td>2</td>\n", "      <td>0.080000</td>\n", "      <td>10.0</td>\n", "      <td>10.00</td>\n", "      <td>10.0</td>\n", "      <td>中频搜索词</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1583</th>\n", "      <td>上允</td>\n", "      <td>26</td>\n", "      <td>24</td>\n", "      <td>202</td>\n", "      <td>2</td>\n", "      <td>0.009901</td>\n", "      <td>2</td>\n", "      <td>0.076923</td>\n", "      <td>8.5</td>\n", "      <td>10.75</td>\n", "      <td>12.1</td>\n", "      <td>中频搜索词</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11986</th>\n", "      <td>欧芹</td>\n", "      <td>22</td>\n", "      <td>23</td>\n", "      <td>118</td>\n", "      <td>0</td>\n", "      <td>0.000000</td>\n", "      <td>0</td>\n", "      <td>0.000000</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>中频搜索词</td>\n", "    </tr>\n", "    <tr>\n", "      <th>19751</th>\n", "      <td>蜜宝</td>\n", "      <td>16</td>\n", "      <td>23</td>\n", "      <td>183</td>\n", "      <td>2</td>\n", "      <td>0.010929</td>\n", "      <td>1</td>\n", "      <td>0.062500</td>\n", "      <td>0.5</td>\n", "      <td>0.75</td>\n", "      <td>0.9</td>\n", "      <td>中频搜索词</td>\n", "    </tr>\n", "    <tr>\n", "      <th>15935</th>\n", "      <td>米酒</td>\n", "      <td>24</td>\n", "      <td>23</td>\n", "      <td>356</td>\n", "      <td>2</td>\n", "      <td>0.005618</td>\n", "      <td>2</td>\n", "      <td>0.083333</td>\n", "      <td>13.0</td>\n", "      <td>14.50</td>\n", "      <td>15.4</td>\n", "      <td>中频搜索词</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13457</th>\n", "      <td>烤培油</td>\n", "      <td>22</td>\n", "      <td>22</td>\n", "      <td>237</td>\n", "      <td>2</td>\n", "      <td>0.008439</td>\n", "      <td>2</td>\n", "      <td>0.090909</td>\n", "      <td>10.5</td>\n", "      <td>13.75</td>\n", "      <td>15.7</td>\n", "      <td>中频搜索词</td>\n", "    </tr>\n", "    <tr>\n", "      <th>18766</th>\n", "      <td>莫奈花园</td>\n", "      <td>23</td>\n", "      <td>22</td>\n", "      <td>145</td>\n", "      <td>1</td>\n", "      <td>0.006897</td>\n", "      <td>1</td>\n", "      <td>0.043478</td>\n", "      <td>16.0</td>\n", "      <td>16.00</td>\n", "      <td>16.0</td>\n", "      <td>中频搜索词</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5089</th>\n", "      <td>嘉利宝54.5%黑巧克力粒</td>\n", "      <td>1</td>\n", "      <td>21</td>\n", "      <td>84</td>\n", "      <td>0</td>\n", "      <td>0.000000</td>\n", "      <td>0</td>\n", "      <td>0.000000</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>中频搜索词</td>\n", "    </tr>\n", "    <tr>\n", "      <th>20620</th>\n", "      <td>迪比克塑型稀奶油</td>\n", "      <td>1</td>\n", "      <td>21</td>\n", "      <td>77</td>\n", "      <td>0</td>\n", "      <td>0.000000</td>\n", "      <td>0</td>\n", "      <td>0.000000</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>中频搜索词</td>\n", "    </tr>\n", "    <tr>\n", "      <th>20564</th>\n", "      <td>运费</td>\n", "      <td>20</td>\n", "      <td>20</td>\n", "      <td>46</td>\n", "      <td>1</td>\n", "      <td>0.021739</td>\n", "      <td>1</td>\n", "      <td>0.050000</td>\n", "      <td>0.0</td>\n", "      <td>0.00</td>\n", "      <td>0.0</td>\n", "      <td>中频搜索词</td>\n", "    </tr>\n", "    <tr>\n", "      <th>17663</th>\n", "      <td>艾许黄油</td>\n", "      <td>17</td>\n", "      <td>20</td>\n", "      <td>197</td>\n", "      <td>4</td>\n", "      <td>0.020305</td>\n", "      <td>1</td>\n", "      <td>0.058824</td>\n", "      <td>17.5</td>\n", "      <td>23.25</td>\n", "      <td>25.5</td>\n", "      <td>中频搜索词</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12583</th>\n", "      <td>法采</td>\n", "      <td>20</td>\n", "      <td>20</td>\n", "      <td>180</td>\n", "      <td>1</td>\n", "      <td>0.005556</td>\n", "      <td>1</td>\n", "      <td>0.050000</td>\n", "      <td>31.0</td>\n", "      <td>31.00</td>\n", "      <td>31.0</td>\n", "      <td>中频搜索词</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2898</th>\n", "      <td>冰博</td>\n", "      <td>21</td>\n", "      <td>20</td>\n", "      <td>132</td>\n", "      <td>2</td>\n", "      <td>0.015152</td>\n", "      <td>2</td>\n", "      <td>0.095238</td>\n", "      <td>6.5</td>\n", "      <td>7.75</td>\n", "      <td>8.5</td>\n", "      <td>中频搜索词</td>\n", "    </tr>\n", "    <tr>\n", "      <th>21868</th>\n", "      <td>青缇</td>\n", "      <td>20</td>\n", "      <td>20</td>\n", "      <td>194</td>\n", "      <td>0</td>\n", "      <td>0.000000</td>\n", "      <td>0</td>\n", "      <td>0.000000</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>中频搜索词</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6856</th>\n", "      <td>安佳再制切达干酪</td>\n", "      <td>1</td>\n", "      <td>20</td>\n", "      <td>80</td>\n", "      <td>0</td>\n", "      <td>0.000000</td>\n", "      <td>0</td>\n", "      <td>0.000000</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>中频搜索词</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11610</th>\n", "      <td>榛果</td>\n", "      <td>21</td>\n", "      <td>19</td>\n", "      <td>363</td>\n", "      <td>4</td>\n", "      <td>0.011019</td>\n", "      <td>2</td>\n", "      <td>0.095238</td>\n", "      <td>23.0</td>\n", "      <td>26.00</td>\n", "      <td>31.4</td>\n", "      <td>中频搜索词</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5210</th>\n", "      <td>国产爱护咖啡奶</td>\n", "      <td>1</td>\n", "      <td>19</td>\n", "      <td>73</td>\n", "      <td>0</td>\n", "      <td>0.000000</td>\n", "      <td>0</td>\n", "      <td>0.000000</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>中频搜索词</td>\n", "    </tr>\n", "    <tr>\n", "      <th>20558</th>\n", "      <td>过滤</td>\n", "      <td>5</td>\n", "      <td>18</td>\n", "      <td>103</td>\n", "      <td>0</td>\n", "      <td>0.000000</td>\n", "      <td>0</td>\n", "      <td>0.000000</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>中频搜索词</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13788</th>\n", "      <td>爱尔优</td>\n", "      <td>17</td>\n", "      <td>18</td>\n", "      <td>125</td>\n", "      <td>1</td>\n", "      <td>0.008000</td>\n", "      <td>1</td>\n", "      <td>0.058824</td>\n", "      <td>4.0</td>\n", "      <td>4.00</td>\n", "      <td>4.0</td>\n", "      <td>中频搜索词</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3869</th>\n", "      <td>卡士</td>\n", "      <td>15</td>\n", "      <td>18</td>\n", "      <td>275</td>\n", "      <td>1</td>\n", "      <td>0.003636</td>\n", "      <td>1</td>\n", "      <td>0.066667</td>\n", "      <td>4.0</td>\n", "      <td>4.00</td>\n", "      <td>4.0</td>\n", "      <td>中频搜索词</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12253</th>\n", "      <td>汤力</td>\n", "      <td>20</td>\n", "      <td>18</td>\n", "      <td>199</td>\n", "      <td>0</td>\n", "      <td>0.000000</td>\n", "      <td>0</td>\n", "      <td>0.000000</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>中频搜索词</td>\n", "    </tr>\n", "    <tr>\n", "      <th>19209</th>\n", "      <td>蓝米吉稀奶油</td>\n", "      <td>1</td>\n", "      <td>17</td>\n", "      <td>67</td>\n", "      <td>0</td>\n", "      <td>0.000000</td>\n", "      <td>0</td>\n", "      <td>0.000000</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>中频搜索词</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10287</th>\n", "      <td>有盐</td>\n", "      <td>18</td>\n", "      <td>17</td>\n", "      <td>169</td>\n", "      <td>0</td>\n", "      <td>0.000000</td>\n", "      <td>0</td>\n", "      <td>0.000000</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>中频搜索词</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3748</th>\n", "      <td>南侨液态酥油</td>\n", "      <td>13</td>\n", "      <td>17</td>\n", "      <td>103</td>\n", "      <td>1</td>\n", "      <td>0.009709</td>\n", "      <td>1</td>\n", "      <td>0.076923</td>\n", "      <td>1.0</td>\n", "      <td>1.00</td>\n", "      <td>1.0</td>\n", "      <td>中频搜索词</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10235</th>\n", "      <td>智利4J樱桃李</td>\n", "      <td>14</td>\n", "      <td>17</td>\n", "      <td>114</td>\n", "      <td>1</td>\n", "      <td>0.008772</td>\n", "      <td>1</td>\n", "      <td>0.071429</td>\n", "      <td>36.0</td>\n", "      <td>36.00</td>\n", "      <td>36.0</td>\n", "      <td>中频搜索词</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7096</th>\n", "      <td>安佳碎条状马苏里拉干酪12kg</td>\n", "      <td>1</td>\n", "      <td>16</td>\n", "      <td>72</td>\n", "      <td>0</td>\n", "      <td>0.000000</td>\n", "      <td>0</td>\n", "      <td>0.000000</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>中频搜索词</td>\n", "    </tr>\n", "    <tr>\n", "      <th>15605</th>\n", "      <td>碱水</td>\n", "      <td>14</td>\n", "      <td>16</td>\n", "      <td>97</td>\n", "      <td>1</td>\n", "      <td>0.010309</td>\n", "      <td>1</td>\n", "      <td>0.071429</td>\n", "      <td>8.0</td>\n", "      <td>8.00</td>\n", "      <td>8.0</td>\n", "      <td>中频搜索词</td>\n", "    </tr>\n", "    <tr>\n", "      <th>21071</th>\n", "      <td>金煌</td>\n", "      <td>13</td>\n", "      <td>16</td>\n", "      <td>119</td>\n", "      <td>0</td>\n", "      <td>0.000000</td>\n", "      <td>0</td>\n", "      <td>0.000000</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>中频搜索词</td>\n", "    </tr>\n", "    <tr>\n", "      <th>19515</th>\n", "      <td>虾</td>\n", "      <td>11</td>\n", "      <td>15</td>\n", "      <td>65</td>\n", "      <td>0</td>\n", "      <td>0.000000</td>\n", "      <td>0</td>\n", "      <td>0.000000</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>中频搜索词</td>\n", "    </tr>\n", "    <tr>\n", "      <th>23117</th>\n", "      <td>麦青汁</td>\n", "      <td>12</td>\n", "      <td>15</td>\n", "      <td>114</td>\n", "      <td>3</td>\n", "      <td>0.026316</td>\n", "      <td>1</td>\n", "      <td>0.083333</td>\n", "      <td>10.0</td>\n", "      <td>10.50</td>\n", "      <td>10.8</td>\n", "      <td>中频搜索词</td>\n", "    </tr>\n", "    <tr>\n", "      <th>15892</th>\n", "      <td>筛</td>\n", "      <td>5</td>\n", "      <td>15</td>\n", "      <td>87</td>\n", "      <td>0</td>\n", "      <td>0.000000</td>\n", "      <td>0</td>\n", "      <td>0.000000</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>中频搜索词</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16753</th>\n", "      <td>维意</td>\n", "      <td>16</td>\n", "      <td>15</td>\n", "      <td>142</td>\n", "      <td>1</td>\n", "      <td>0.007042</td>\n", "      <td>1</td>\n", "      <td>0.062500</td>\n", "      <td>10.0</td>\n", "      <td>10.00</td>\n", "      <td>10.0</td>\n", "      <td>中频搜索词</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10261</th>\n", "      <td>曲奇</td>\n", "      <td>14</td>\n", "      <td>14</td>\n", "      <td>150</td>\n", "      <td>1</td>\n", "      <td>0.006667</td>\n", "      <td>1</td>\n", "      <td>0.071429</td>\n", "      <td>4.0</td>\n", "      <td>4.00</td>\n", "      <td>4.0</td>\n", "      <td>中频搜索词</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                 query  searched_users  searched_times  impression_cnt  \\\n", "22803               鲜沐             157             168             838   \n", "14352              玉米油             133             143            1531   \n", "17584              色拉油              53              51             404   \n", "12706            泰象矿泉水              35              44             247   \n", "2586               保鲜膜              43              42             476   \n", "4520               君乐宝              33              36             326   \n", "2895               冰勃朗              35              34             349   \n", "10985               树梅              25              31             215   \n", "20910              酸黄瓜              26              30             342   \n", "16729                维              29              29             352   \n", "5540               大多肉              29              29             201   \n", "6173               奶油卡              28              26             269   \n", "5953                奥利              29              26             225   \n", "12759               测试               6              26             120   \n", "17653              艾素糖              25              25             304   \n", "12014               正荣              23              25             177   \n", "6741                季歌              19              24             158   \n", "2236                 优              25              24             168   \n", "1583                上允              26              24             202   \n", "11986               欧芹              22              23             118   \n", "19751               蜜宝              16              23             183   \n", "15935               米酒              24              23             356   \n", "13457              烤培油              22              22             237   \n", "18766             莫奈花园              23              22             145   \n", "5089     嘉利宝54.5%黑巧克力粒               1              21              84   \n", "20620         迪比克塑型稀奶油               1              21              77   \n", "20564               运费              20              20              46   \n", "17663             艾许黄油              17              20             197   \n", "12583               法采              20              20             180   \n", "2898                冰博              21              20             132   \n", "21868               青缇              20              20             194   \n", "6856          安佳再制切达干酪               1              20              80   \n", "11610               榛果              21              19             363   \n", "5210           国产爱护咖啡奶               1              19              73   \n", "20558               过滤               5              18             103   \n", "13788              爱尔优              17              18             125   \n", "3869                卡士              15              18             275   \n", "12253               汤力              20              18             199   \n", "19209           蓝米吉稀奶油               1              17              67   \n", "10287               有盐              18              17             169   \n", "3748            南侨液态酥油              13              17             103   \n", "10235          智利4J樱桃李              14              17             114   \n", "7096   安佳碎条状马苏里拉干酪12kg               1              16              72   \n", "15605               碱水              14              16              97   \n", "21071               金煌              13              16             119   \n", "19515                虾              11              15              65   \n", "23117              麦青汁              12              15             114   \n", "15892                筛               5              15              87   \n", "16753               维意              16              15             142   \n", "10261               曲奇              14              14             150   \n", "\n", "       click_cnt   sku_ctr  clicked_user_cnt  user_ctr  p50_click_index  \\\n", "22803         17  0.020286                15  0.096154              3.0   \n", "14352         15  0.009798                11  0.082707              9.0   \n", "17584          6  0.014851                 4  0.075472              3.5   \n", "12706          5  0.020243                 3  0.085714              3.0   \n", "2586           4  0.008403                 4  0.093023              4.0   \n", "4520           2  0.006135                 2  0.060606              4.0   \n", "2895           3  0.008596                 3  0.085714              4.0   \n", "10985          7  0.032558                 2  0.080000              4.0   \n", "20910          2  0.005848                 2  0.076923              5.5   \n", "16729          1  0.002841                 1  0.035714              4.0   \n", "5540           0  0.000000                 0  0.000000              NaN   \n", "6173           2  0.007435                 2  0.071429              3.0   \n", "5953           1  0.004444                 1  0.034483             22.0   \n", "12759          0  0.000000                 0  0.000000              NaN   \n", "17653          2  0.006579                 2  0.080000              6.0   \n", "12014          2  0.011299                 2  0.086957              7.0   \n", "6741           1  0.006329                 1  0.052632              0.0   \n", "2236           2  0.011905                 2  0.080000             10.0   \n", "1583           2  0.009901                 2  0.076923              8.5   \n", "11986          0  0.000000                 0  0.000000              NaN   \n", "19751          2  0.010929                 1  0.062500              0.5   \n", "15935          2  0.005618                 2  0.083333             13.0   \n", "13457          2  0.008439                 2  0.090909             10.5   \n", "18766          1  0.006897                 1  0.043478             16.0   \n", "5089           0  0.000000                 0  0.000000              NaN   \n", "20620          0  0.000000                 0  0.000000              NaN   \n", "20564          1  0.021739                 1  0.050000              0.0   \n", "17663          4  0.020305                 1  0.058824             17.5   \n", "12583          1  0.005556                 1  0.050000             31.0   \n", "2898           2  0.015152                 2  0.095238              6.5   \n", "21868          0  0.000000                 0  0.000000              NaN   \n", "6856           0  0.000000                 0  0.000000              NaN   \n", "11610          4  0.011019                 2  0.095238             23.0   \n", "5210           0  0.000000                 0  0.000000              NaN   \n", "20558          0  0.000000                 0  0.000000              NaN   \n", "13788          1  0.008000                 1  0.058824              4.0   \n", "3869           1  0.003636                 1  0.066667              4.0   \n", "12253          0  0.000000                 0  0.000000              NaN   \n", "19209          0  0.000000                 0  0.000000              NaN   \n", "10287          0  0.000000                 0  0.000000              NaN   \n", "3748           1  0.009709                 1  0.076923              1.0   \n", "10235          1  0.008772                 1  0.071429             36.0   \n", "7096           0  0.000000                 0  0.000000              NaN   \n", "15605          1  0.010309                 1  0.071429              8.0   \n", "21071          0  0.000000                 0  0.000000              NaN   \n", "19515          0  0.000000                 0  0.000000              NaN   \n", "23117          3  0.026316                 1  0.083333             10.0   \n", "15892          0  0.000000                 0  0.000000              NaN   \n", "16753          1  0.007042                 1  0.062500             10.0   \n", "10261          1  0.006667                 1  0.071429              4.0   \n", "\n", "       p75_click_index  p90_click_index 搜索频次标签  \n", "22803             4.00              4.4  中频搜索词  \n", "14352            10.00             14.6  中频搜索词  \n", "17584             4.00              7.0  中频搜索词  \n", "12706             5.00              5.0  中频搜索词  \n", "2586              4.25              4.7  中频搜索词  \n", "4520              4.00              4.0  中频搜索词  \n", "2895              7.50              9.6  中频搜索词  \n", "10985             4.00              4.0  中频搜索词  \n", "20910             7.75              9.1  中频搜索词  \n", "16729             4.00              4.0  中频搜索词  \n", "5540               NaN              NaN  中频搜索词  \n", "6173              3.50              3.8  中频搜索词  \n", "5953             22.00             22.0  中频搜索词  \n", "12759              NaN              NaN  中频搜索词  \n", "17653             7.00              7.6  中频搜索词  \n", "12014             9.00             10.2  中频搜索词  \n", "6741              0.00              0.0  中频搜索词  \n", "2236             10.00             10.0  中频搜索词  \n", "1583             10.75             12.1  中频搜索词  \n", "11986              NaN              NaN  中频搜索词  \n", "19751             0.75              0.9  中频搜索词  \n", "15935            14.50             15.4  中频搜索词  \n", "13457            13.75             15.7  中频搜索词  \n", "18766            16.00             16.0  中频搜索词  \n", "5089               NaN              NaN  中频搜索词  \n", "20620              NaN              NaN  中频搜索词  \n", "20564             0.00              0.0  中频搜索词  \n", "17663            23.25             25.5  中频搜索词  \n", "12583            31.00             31.0  中频搜索词  \n", "2898              7.75              8.5  中频搜索词  \n", "21868              NaN              NaN  中频搜索词  \n", "6856               NaN              NaN  中频搜索词  \n", "11610            26.00             31.4  中频搜索词  \n", "5210               NaN              NaN  中频搜索词  \n", "20558              NaN              NaN  中频搜索词  \n", "13788             4.00              4.0  中频搜索词  \n", "3869              4.00              4.0  中频搜索词  \n", "12253              NaN              NaN  中频搜索词  \n", "19209              NaN              NaN  中频搜索词  \n", "10287              NaN              NaN  中频搜索词  \n", "3748              1.00              1.0  中频搜索词  \n", "10235            36.00             36.0  中频搜索词  \n", "7096               NaN              NaN  中频搜索词  \n", "15605             8.00              8.0  中频搜索词  \n", "21071              NaN              NaN  中频搜索词  \n", "19515              NaN              NaN  中频搜索词  \n", "23117            10.50             10.8  中频搜索词  \n", "15892              NaN              NaN  中频搜索词  \n", "16753            10.00             10.0  中频搜索词  \n", "10261             4.00              4.0  中频搜索词  "]}, "execution_count": 19, "metadata": {}, "output_type": "execute_result"}], "source": ["print(medium_df[medium_df['user_ctr']<0.1].describe())\n", "medium_df[medium_df['user_ctr']<0.1].sort_values(by=['searched_times'], ascending=False).head(50)"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>query</th>\n", "      <th>searched_users</th>\n", "      <th>searched_times</th>\n", "      <th>impression_cnt</th>\n", "      <th>click_cnt</th>\n", "      <th>sku_ctr</th>\n", "      <th>clicked_user_cnt</th>\n", "      <th>user_ctr</th>\n", "      <th>p50_click_index</th>\n", "      <th>p75_click_index</th>\n", "      <th>p90_click_index</th>\n", "      <th>搜索频次标签</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>7666</th>\n", "      <td>寒天</td>\n", "      <td>193</td>\n", "      <td>223</td>\n", "      <td>1981</td>\n", "      <td>40</td>\n", "      <td>0.020192</td>\n", "      <td>35</td>\n", "      <td>0.181347</td>\n", "      <td>9.0</td>\n", "      <td>11.00</td>\n", "      <td>23.5</td>\n", "      <td>中频搜索词</td>\n", "    </tr>\n", "    <tr>\n", "      <th>19980</th>\n", "      <td>西梅</td>\n", "      <td>130</td>\n", "      <td>210</td>\n", "      <td>1641</td>\n", "      <td>69</td>\n", "      <td>0.042048</td>\n", "      <td>35</td>\n", "      <td>0.269231</td>\n", "      <td>1.0</td>\n", "      <td>3.00</td>\n", "      <td>5.0</td>\n", "      <td>中频搜索词</td>\n", "    </tr>\n", "    <tr>\n", "      <th>21922</th>\n", "      <td>面包</td>\n", "      <td>202</td>\n", "      <td>200</td>\n", "      <td>4833</td>\n", "      <td>74</td>\n", "      <td>0.015311</td>\n", "      <td>51</td>\n", "      <td>0.252475</td>\n", "      <td>21.5</td>\n", "      <td>28.00</td>\n", "      <td>45.4</td>\n", "      <td>中频搜索词</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10071</th>\n", "      <td>明治</td>\n", "      <td>178</td>\n", "      <td>197</td>\n", "      <td>1804</td>\n", "      <td>44</td>\n", "      <td>0.024390</td>\n", "      <td>33</td>\n", "      <td>0.185393</td>\n", "      <td>4.5</td>\n", "      <td>16.25</td>\n", "      <td>24.7</td>\n", "      <td>中频搜索词</td>\n", "    </tr>\n", "    <tr>\n", "      <th>15554</th>\n", "      <td>砂糖橘</td>\n", "      <td>98</td>\n", "      <td>196</td>\n", "      <td>1459</td>\n", "      <td>76</td>\n", "      <td>0.052090</td>\n", "      <td>34</td>\n", "      <td>0.346939</td>\n", "      <td>0.0</td>\n", "      <td>1.25</td>\n", "      <td>3.0</td>\n", "      <td>中频搜索词</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8195</th>\n", "      <td>巧克力酱</td>\n", "      <td>183</td>\n", "      <td>196</td>\n", "      <td>4421</td>\n", "      <td>94</td>\n", "      <td>0.021262</td>\n", "      <td>63</td>\n", "      <td>0.344262</td>\n", "      <td>10.0</td>\n", "      <td>26.75</td>\n", "      <td>38.7</td>\n", "      <td>中频搜索词</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10311</th>\n", "      <td>朗姆酒</td>\n", "      <td>132</td>\n", "      <td>179</td>\n", "      <td>2588</td>\n", "      <td>68</td>\n", "      <td>0.026275</td>\n", "      <td>45</td>\n", "      <td>0.340909</td>\n", "      <td>5.5</td>\n", "      <td>11.00</td>\n", "      <td>17.0</td>\n", "      <td>中频搜索词</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5442</th>\n", "      <td>夏黑</td>\n", "      <td>105</td>\n", "      <td>179</td>\n", "      <td>1642</td>\n", "      <td>73</td>\n", "      <td>0.044458</td>\n", "      <td>39</td>\n", "      <td>0.371429</td>\n", "      <td>1.0</td>\n", "      <td>8.00</td>\n", "      <td>16.8</td>\n", "      <td>中频搜索词</td>\n", "    </tr>\n", "    <tr>\n", "      <th>22803</th>\n", "      <td>鲜沐</td>\n", "      <td>157</td>\n", "      <td>168</td>\n", "      <td>838</td>\n", "      <td>17</td>\n", "      <td>0.020286</td>\n", "      <td>15</td>\n", "      <td>0.096154</td>\n", "      <td>3.0</td>\n", "      <td>4.00</td>\n", "      <td>4.4</td>\n", "      <td>中频搜索词</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11143</th>\n", "      <td>桃</td>\n", "      <td>143</td>\n", "      <td>165</td>\n", "      <td>3321</td>\n", "      <td>87</td>\n", "      <td>0.026197</td>\n", "      <td>57</td>\n", "      <td>0.398601</td>\n", "      <td>9.0</td>\n", "      <td>23.00</td>\n", "      <td>34.0</td>\n", "      <td>中频搜索词</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4338</th>\n", "      <td>可颂</td>\n", "      <td>134</td>\n", "      <td>157</td>\n", "      <td>2746</td>\n", "      <td>60</td>\n", "      <td>0.021850</td>\n", "      <td>40</td>\n", "      <td>0.298507</td>\n", "      <td>11.0</td>\n", "      <td>27.00</td>\n", "      <td>40.5</td>\n", "      <td>中频搜索词</td>\n", "    </tr>\n", "    <tr>\n", "      <th>15527</th>\n", "      <td>石榴</td>\n", "      <td>113</td>\n", "      <td>152</td>\n", "      <td>1677</td>\n", "      <td>42</td>\n", "      <td>0.025045</td>\n", "      <td>28</td>\n", "      <td>0.247788</td>\n", "      <td>8.0</td>\n", "      <td>28.00</td>\n", "      <td>39.3</td>\n", "      <td>中频搜索词</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2901</th>\n", "      <td>冰博客</td>\n", "      <td>133</td>\n", "      <td>147</td>\n", "      <td>1470</td>\n", "      <td>30</td>\n", "      <td>0.020408</td>\n", "      <td>23</td>\n", "      <td>0.172932</td>\n", "      <td>5.0</td>\n", "      <td>19.25</td>\n", "      <td>23.1</td>\n", "      <td>中频搜索词</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14352</th>\n", "      <td>玉米油</td>\n", "      <td>133</td>\n", "      <td>143</td>\n", "      <td>1531</td>\n", "      <td>15</td>\n", "      <td>0.009798</td>\n", "      <td>11</td>\n", "      <td>0.082707</td>\n", "      <td>9.0</td>\n", "      <td>10.00</td>\n", "      <td>14.6</td>\n", "      <td>中频搜索词</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4887</th>\n", "      <td>咸蛋黄</td>\n", "      <td>135</td>\n", "      <td>143</td>\n", "      <td>2013</td>\n", "      <td>35</td>\n", "      <td>0.017387</td>\n", "      <td>26</td>\n", "      <td>0.192593</td>\n", "      <td>10.0</td>\n", "      <td>17.00</td>\n", "      <td>43.0</td>\n", "      <td>中频搜索词</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14348</th>\n", "      <td>玉米</td>\n", "      <td>130</td>\n", "      <td>140</td>\n", "      <td>2088</td>\n", "      <td>44</td>\n", "      <td>0.021073</td>\n", "      <td>28</td>\n", "      <td>0.215385</td>\n", "      <td>10.0</td>\n", "      <td>25.00</td>\n", "      <td>28.0</td>\n", "      <td>中频搜索词</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12956</th>\n", "      <td>淀粉</td>\n", "      <td>101</td>\n", "      <td>138</td>\n", "      <td>1882</td>\n", "      <td>40</td>\n", "      <td>0.021254</td>\n", "      <td>36</td>\n", "      <td>0.356436</td>\n", "      <td>2.0</td>\n", "      <td>5.00</td>\n", "      <td>9.0</td>\n", "      <td>中频搜索词</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4247</th>\n", "      <td>可可百利</td>\n", "      <td>105</td>\n", "      <td>137</td>\n", "      <td>1806</td>\n", "      <td>57</td>\n", "      <td>0.031561</td>\n", "      <td>39</td>\n", "      <td>0.371429</td>\n", "      <td>5.0</td>\n", "      <td>16.00</td>\n", "      <td>28.3</td>\n", "      <td>中频搜索词</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7887</th>\n", "      <td>小麦草</td>\n", "      <td>74</td>\n", "      <td>136</td>\n", "      <td>807</td>\n", "      <td>40</td>\n", "      <td>0.049566</td>\n", "      <td>23</td>\n", "      <td>0.310811</td>\n", "      <td>0.0</td>\n", "      <td>0.00</td>\n", "      <td>0.0</td>\n", "      <td>中频搜索词</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10410</th>\n", "      <td>杏仁</td>\n", "      <td>92</td>\n", "      <td>136</td>\n", "      <td>1386</td>\n", "      <td>52</td>\n", "      <td>0.037518</td>\n", "      <td>34</td>\n", "      <td>0.369565</td>\n", "      <td>2.0</td>\n", "      <td>16.00</td>\n", "      <td>24.4</td>\n", "      <td>中频搜索词</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["      query  searched_users  searched_times  impression_cnt  click_cnt  \\\n", "7666     寒天             193             223            1981         40   \n", "19980    西梅             130             210            1641         69   \n", "21922    面包             202             200            4833         74   \n", "10071    明治             178             197            1804         44   \n", "15554   砂糖橘              98             196            1459         76   \n", "8195   巧克力酱             183             196            4421         94   \n", "10311   朗姆酒             132             179            2588         68   \n", "5442     夏黑             105             179            1642         73   \n", "22803    鲜沐             157             168             838         17   \n", "11143     桃             143             165            3321         87   \n", "4338     可颂             134             157            2746         60   \n", "15527    石榴             113             152            1677         42   \n", "2901    冰博客             133             147            1470         30   \n", "14352   玉米油             133             143            1531         15   \n", "4887    咸蛋黄             135             143            2013         35   \n", "14348    玉米             130             140            2088         44   \n", "12956    淀粉             101             138            1882         40   \n", "4247   可可百利             105             137            1806         57   \n", "7887    小麦草              74             136             807         40   \n", "10410    杏仁              92             136            1386         52   \n", "\n", "        sku_ctr  clicked_user_cnt  user_ctr  p50_click_index  p75_click_index  \\\n", "7666   0.020192                35  0.181347              9.0            11.00   \n", "19980  0.042048                35  0.269231              1.0             3.00   \n", "21922  0.015311                51  0.252475             21.5            28.00   \n", "10071  0.024390                33  0.185393              4.5            16.25   \n", "15554  0.052090                34  0.346939              0.0             1.25   \n", "8195   0.021262                63  0.344262             10.0            26.75   \n", "10311  0.026275                45  0.340909              5.5            11.00   \n", "5442   0.044458                39  0.371429              1.0             8.00   \n", "22803  0.020286                15  0.096154              3.0             4.00   \n", "11143  0.026197                57  0.398601              9.0            23.00   \n", "4338   0.021850                40  0.298507             11.0            27.00   \n", "15527  0.025045                28  0.247788              8.0            28.00   \n", "2901   0.020408                23  0.172932              5.0            19.25   \n", "14352  0.009798                11  0.082707              9.0            10.00   \n", "4887   0.017387                26  0.192593             10.0            17.00   \n", "14348  0.021073                28  0.215385             10.0            25.00   \n", "12956  0.021254                36  0.356436              2.0             5.00   \n", "4247   0.031561                39  0.371429              5.0            16.00   \n", "7887   0.049566                23  0.310811              0.0             0.00   \n", "10410  0.037518                34  0.369565              2.0            16.00   \n", "\n", "       p90_click_index 搜索频次标签  \n", "7666              23.5  中频搜索词  \n", "19980              5.0  中频搜索词  \n", "21922             45.4  中频搜索词  \n", "10071             24.7  中频搜索词  \n", "15554              3.0  中频搜索词  \n", "8195              38.7  中频搜索词  \n", "10311             17.0  中频搜索词  \n", "5442              16.8  中频搜索词  \n", "22803              4.4  中频搜索词  \n", "11143             34.0  中频搜索词  \n", "4338              40.5  中频搜索词  \n", "15527             39.3  中频搜索词  \n", "2901              23.1  中频搜索词  \n", "14352             14.6  中频搜索词  \n", "4887              43.0  中频搜索词  \n", "14348             28.0  中频搜索词  \n", "12956              9.0  中频搜索词  \n", "4247              28.3  中频搜索词  \n", "7887               0.0  中频搜索词  \n", "10410             24.4  中频搜索词  "]}, "execution_count": 20, "metadata": {}, "output_type": "execute_result"}], "source": ["medium_df[medium_df[\"user_ctr\"] < 0.4].sort_values(\n", "    by=[\"searched_times\"], ascending=False\n", ").head(20)\n"]}, {"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>query</th>\n", "      <th>searched_users</th>\n", "      <th>searched_times</th>\n", "      <th>impression_cnt</th>\n", "      <th>click_cnt</th>\n", "      <th>sku_ctr</th>\n", "      <th>clicked_user_cnt</th>\n", "      <th>user_ctr</th>\n", "      <th>p50_click_index</th>\n", "      <th>p75_click_index</th>\n", "      <th>p90_click_index</th>\n", "      <th>搜索频次标签</th>\n", "      <th>link</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>5803</th>\n", "      <td>喷射奶油</td>\n", "      <td>204</td>\n", "      <td>207</td>\n", "      <td>5181</td>\n", "      <td>82</td>\n", "      <td>0.015827</td>\n", "      <td>58</td>\n", "      <td>0.284314</td>\n", "      <td>33.0</td>\n", "      <td>59.75</td>\n", "      <td>85.9</td>\n", "      <td>中频搜索词</td>\n", "      <td>http://xianmuai.s7.tunnelfrp.com/search-arena/?query=喷射奶油&amp;page_size=40&amp;city=杭州</td>\n", "    </tr>\n", "    <tr>\n", "      <th>18287</th>\n", "      <td>罐头</td>\n", "      <td>150</td>\n", "      <td>200</td>\n", "      <td>5701</td>\n", "      <td>197</td>\n", "      <td>0.034555</td>\n", "      <td>81</td>\n", "      <td>0.540000</td>\n", "      <td>21.0</td>\n", "      <td>32.75</td>\n", "      <td>47.0</td>\n", "      <td>中频搜索词</td>\n", "      <td>http://xianmuai.s7.tunnelfrp.com/search-arena/?query=罐头&amp;page_size=40&amp;city=杭州</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3629</th>\n", "      <td>冷冻</td>\n", "      <td>131</td>\n", "      <td>129</td>\n", "      <td>2904</td>\n", "      <td>138</td>\n", "      <td>0.047521</td>\n", "      <td>62</td>\n", "      <td>0.473282</td>\n", "      <td>17.5</td>\n", "      <td>40.50</td>\n", "      <td>79.9</td>\n", "      <td>中频搜索词</td>\n", "      <td>http://xianmuai.s7.tunnelfrp.com/search-arena/?query=冷冻&amp;page_size=40&amp;city=杭州</td>\n", "    </tr>\n", "    <tr>\n", "      <th>15498</th>\n", "      <td>玉米油</td>\n", "      <td>129</td>\n", "      <td>121</td>\n", "      <td>1393</td>\n", "      <td>10</td>\n", "      <td>0.007179</td>\n", "      <td>12</td>\n", "      <td>0.093023</td>\n", "      <td>18.0</td>\n", "      <td>18.00</td>\n", "      <td>20.9</td>\n", "      <td>中频搜索词</td>\n", "      <td>http://xianmuai.s7.tunnelfrp.com/search-arena/?query=玉米油&amp;page_size=40&amp;city=杭州</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11419</th>\n", "      <td>果汁</td>\n", "      <td>124</td>\n", "      <td>131</td>\n", "      <td>3914</td>\n", "      <td>73</td>\n", "      <td>0.018651</td>\n", "      <td>49</td>\n", "      <td>0.395161</td>\n", "      <td>13.0</td>\n", "      <td>37.00</td>\n", "      <td>73.4</td>\n", "      <td>中频搜索词</td>\n", "      <td>http://xianmuai.s7.tunnelfrp.com/search-arena/?query=果汁&amp;page_size=40&amp;city=杭州</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5046</th>\n", "      <td>可颂</td>\n", "      <td>112</td>\n", "      <td>116</td>\n", "      <td>2085</td>\n", "      <td>33</td>\n", "      <td>0.015827</td>\n", "      <td>28</td>\n", "      <td>0.250000</td>\n", "      <td>18.0</td>\n", "      <td>36.00</td>\n", "      <td>62.8</td>\n", "      <td>中频搜索词</td>\n", "      <td>http://xianmuai.s7.tunnelfrp.com/search-arena/?query=可颂&amp;page_size=40&amp;city=杭州</td>\n", "    </tr>\n", "    <tr>\n", "      <th>20457</th>\n", "      <td>葡萄干</td>\n", "      <td>109</td>\n", "      <td>111</td>\n", "      <td>1575</td>\n", "      <td>15</td>\n", "      <td>0.009524</td>\n", "      <td>20</td>\n", "      <td>0.183486</td>\n", "      <td>21.0</td>\n", "      <td>32.00</td>\n", "      <td>58.6</td>\n", "      <td>中频搜索词</td>\n", "      <td>http://xianmuai.s7.tunnelfrp.com/search-arena/?query=葡萄干&amp;page_size=40&amp;city=杭州</td>\n", "    </tr>\n", "    <tr>\n", "      <th>20905</th>\n", "      <td>薄荷糖浆</td>\n", "      <td>101</td>\n", "      <td>107</td>\n", "      <td>2223</td>\n", "      <td>52</td>\n", "      <td>0.023392</td>\n", "      <td>34</td>\n", "      <td>0.336634</td>\n", "      <td>20.0</td>\n", "      <td>26.75</td>\n", "      <td>41.0</td>\n", "      <td>中频搜索词</td>\n", "      <td>http://xianmuai.s7.tunnelfrp.com/search-arena/?query=薄荷糖浆&amp;page_size=40&amp;city=杭州</td>\n", "    </tr>\n", "    <tr>\n", "      <th>17185</th>\n", "      <td>粉</td>\n", "      <td>99</td>\n", "      <td>107</td>\n", "      <td>2821</td>\n", "      <td>76</td>\n", "      <td>0.026941</td>\n", "      <td>45</td>\n", "      <td>0.454545</td>\n", "      <td>17.0</td>\n", "      <td>28.00</td>\n", "      <td>48.6</td>\n", "      <td>中频搜索词</td>\n", "      <td>http://xianmuai.s7.tunnelfrp.com/search-arena/?query=粉&amp;page_size=40&amp;city=杭州</td>\n", "    </tr>\n", "    <tr>\n", "      <th>23826</th>\n", "      <td>饮料</td>\n", "      <td>94</td>\n", "      <td>110</td>\n", "      <td>3187</td>\n", "      <td>59</td>\n", "      <td>0.018513</td>\n", "      <td>40</td>\n", "      <td>0.425532</td>\n", "      <td>35.0</td>\n", "      <td>72.00</td>\n", "      <td>142.2</td>\n", "      <td>中频搜索词</td>\n", "      <td>http://xianmuai.s7.tunnelfrp.com/search-arena/?query=饮料&amp;page_size=40&amp;city=杭州</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16108</th>\n", "      <td>番茄酱</td>\n", "      <td>85</td>\n", "      <td>97</td>\n", "      <td>1519</td>\n", "      <td>20</td>\n", "      <td>0.013167</td>\n", "      <td>17</td>\n", "      <td>0.200000</td>\n", "      <td>21.0</td>\n", "      <td>30.50</td>\n", "      <td>39.0</td>\n", "      <td>中频搜索词</td>\n", "      <td>http://xianmuai.s7.tunnelfrp.com/search-arena/?query=番茄酱&amp;page_size=40&amp;city=杭州</td>\n", "    </tr>\n", "    <tr>\n", "      <th>20985</th>\n", "      <td>蛋</td>\n", "      <td>85</td>\n", "      <td>79</td>\n", "      <td>767</td>\n", "      <td>9</td>\n", "      <td>0.011734</td>\n", "      <td>10</td>\n", "      <td>0.117647</td>\n", "      <td>19.0</td>\n", "      <td>24.00</td>\n", "      <td>27.4</td>\n", "      <td>中频搜索词</td>\n", "      <td>http://xianmuai.s7.tunnelfrp.com/search-arena/?query=蛋&amp;page_size=40&amp;city=杭州</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16361</th>\n", "      <td>白黄油</td>\n", "      <td>83</td>\n", "      <td>86</td>\n", "      <td>1652</td>\n", "      <td>22</td>\n", "      <td>0.013317</td>\n", "      <td>25</td>\n", "      <td>0.301205</td>\n", "      <td>18.5</td>\n", "      <td>27.75</td>\n", "      <td>38.0</td>\n", "      <td>中频搜索词</td>\n", "      <td>http://xianmuai.s7.tunnelfrp.com/search-arena/?query=白黄油&amp;page_size=40&amp;city=杭州</td>\n", "    </tr>\n", "    <tr>\n", "      <th>22063</th>\n", "      <td>达川</td>\n", "      <td>75</td>\n", "      <td>80</td>\n", "      <td>584</td>\n", "      <td>14</td>\n", "      <td>0.023973</td>\n", "      <td>13</td>\n", "      <td>0.173333</td>\n", "      <td>17.5</td>\n", "      <td>18.75</td>\n", "      <td>35.7</td>\n", "      <td>中频搜索词</td>\n", "      <td>http://xianmuai.s7.tunnelfrp.com/search-arena/?query=达川&amp;page_size=40&amp;city=杭州</td>\n", "    </tr>\n", "    <tr>\n", "      <th>22368</th>\n", "      <td>酱</td>\n", "      <td>68</td>\n", "      <td>67</td>\n", "      <td>1676</td>\n", "      <td>35</td>\n", "      <td>0.020883</td>\n", "      <td>18</td>\n", "      <td>0.264706</td>\n", "      <td>15.0</td>\n", "      <td>53.50</td>\n", "      <td>68.6</td>\n", "      <td>中频搜索词</td>\n", "      <td>http://xianmuai.s7.tunnelfrp.com/search-arena/?query=酱&amp;page_size=40&amp;city=杭州</td>\n", "    </tr>\n", "    <tr>\n", "      <th>23335</th>\n", "      <td>青团</td>\n", "      <td>66</td>\n", "      <td>71</td>\n", "      <td>903</td>\n", "      <td>6</td>\n", "      <td>0.006645</td>\n", "      <td>6</td>\n", "      <td>0.090909</td>\n", "      <td>34.5</td>\n", "      <td>53.25</td>\n", "      <td>60.5</td>\n", "      <td>中频搜索词</td>\n", "      <td>http://xianmuai.s7.tunnelfrp.com/search-arena/?query=青团&amp;page_size=40&amp;city=杭州</td>\n", "    </tr>\n", "    <tr>\n", "      <th>21098</th>\n", "      <td>蛋糕盒</td>\n", "      <td>62</td>\n", "      <td>54</td>\n", "      <td>864</td>\n", "      <td>17</td>\n", "      <td>0.019676</td>\n", "      <td>14</td>\n", "      <td>0.225806</td>\n", "      <td>18.0</td>\n", "      <td>82.00</td>\n", "      <td>126.0</td>\n", "      <td>中频搜索词</td>\n", "      <td>http://xianmuai.s7.tunnelfrp.com/search-arena/?query=蛋糕盒&amp;page_size=40&amp;city=杭州</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13240</th>\n", "      <td>汤力水</td>\n", "      <td>59</td>\n", "      <td>63</td>\n", "      <td>796</td>\n", "      <td>6</td>\n", "      <td>0.007538</td>\n", "      <td>7</td>\n", "      <td>0.118644</td>\n", "      <td>18.0</td>\n", "      <td>18.00</td>\n", "      <td>33.0</td>\n", "      <td>中频搜索词</td>\n", "      <td>http://xianmuai.s7.tunnelfrp.com/search-arena/?query=汤力水&amp;page_size=40&amp;city=杭州</td>\n", "    </tr>\n", "    <tr>\n", "      <th>18406</th>\n", "      <td>美枚</td>\n", "      <td>52</td>\n", "      <td>56</td>\n", "      <td>286</td>\n", "      <td>2</td>\n", "      <td>0.006993</td>\n", "      <td>3</td>\n", "      <td>0.057692</td>\n", "      <td>18.5</td>\n", "      <td>18.75</td>\n", "      <td>18.9</td>\n", "      <td>中频搜索词</td>\n", "      <td>http://xianmuai.s7.tunnelfrp.com/search-arena/?query=美枚&amp;page_size=40&amp;city=杭州</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8857</th>\n", "      <td>巴乐</td>\n", "      <td>50</td>\n", "      <td>50</td>\n", "      <td>333</td>\n", "      <td>2</td>\n", "      <td>0.006006</td>\n", "      <td>4</td>\n", "      <td>0.080000</td>\n", "      <td>28.5</td>\n", "      <td>33.25</td>\n", "      <td>36.1</td>\n", "      <td>中频搜索词</td>\n", "      <td>http://xianmuai.s7.tunnelfrp.com/search-arena/?query=巴乐&amp;page_size=40&amp;city=杭州</td>\n", "    </tr>\n", "    <tr>\n", "      <th>21347</th>\n", "      <td>袋</td>\n", "      <td>50</td>\n", "      <td>51</td>\n", "      <td>832</td>\n", "      <td>16</td>\n", "      <td>0.019231</td>\n", "      <td>13</td>\n", "      <td>0.260000</td>\n", "      <td>15.5</td>\n", "      <td>43.25</td>\n", "      <td>69.5</td>\n", "      <td>中频搜索词</td>\n", "      <td>http://xianmuai.s7.tunnelfrp.com/search-arena/?query=袋&amp;page_size=40&amp;city=杭州</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11272</th>\n", "      <td>杨梅果酱</td>\n", "      <td>50</td>\n", "      <td>57</td>\n", "      <td>1280</td>\n", "      <td>23</td>\n", "      <td>0.017969</td>\n", "      <td>14</td>\n", "      <td>0.280000</td>\n", "      <td>6.0</td>\n", "      <td>42.50</td>\n", "      <td>54.0</td>\n", "      <td>中频搜索词</td>\n", "      <td>http://xianmuai.s7.tunnelfrp.com/search-arena/?query=杨梅果酱&amp;page_size=40&amp;city=杭州</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3611</th>\n", "      <td>冰袋</td>\n", "      <td>49</td>\n", "      <td>44</td>\n", "      <td>617</td>\n", "      <td>13</td>\n", "      <td>0.021070</td>\n", "      <td>6</td>\n", "      <td>0.122449</td>\n", "      <td>19.0</td>\n", "      <td>26.00</td>\n", "      <td>27.0</td>\n", "      <td>中频搜索词</td>\n", "      <td>http://xianmuai.s7.tunnelfrp.com/search-arena/?query=冰袋&amp;page_size=40&amp;city=杭州</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3075</th>\n", "      <td>保鲜膜</td>\n", "      <td>47</td>\n", "      <td>47</td>\n", "      <td>387</td>\n", "      <td>2</td>\n", "      <td>0.005168</td>\n", "      <td>4</td>\n", "      <td>0.085106</td>\n", "      <td>18.5</td>\n", "      <td>18.75</td>\n", "      <td>18.9</td>\n", "      <td>中频搜索词</td>\n", "      <td>http://xianmuai.s7.tunnelfrp.com/search-arena/?query=保鲜膜&amp;page_size=40&amp;city=杭州</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9865</th>\n", "      <td>慕斯蛋糕</td>\n", "      <td>46</td>\n", "      <td>43</td>\n", "      <td>2301</td>\n", "      <td>79</td>\n", "      <td>0.034333</td>\n", "      <td>27</td>\n", "      <td>0.586957</td>\n", "      <td>49.0</td>\n", "      <td>67.50</td>\n", "      <td>105.0</td>\n", "      <td>中频搜索词</td>\n", "      <td>http://xianmuai.s7.tunnelfrp.com/search-arena/?query=慕斯蛋糕&amp;page_size=40&amp;city=杭州</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9557</th>\n", "      <td>德馨</td>\n", "      <td>46</td>\n", "      <td>42</td>\n", "      <td>879</td>\n", "      <td>19</td>\n", "      <td>0.021615</td>\n", "      <td>16</td>\n", "      <td>0.347826</td>\n", "      <td>18.0</td>\n", "      <td>40.00</td>\n", "      <td>50.6</td>\n", "      <td>中频搜索词</td>\n", "      <td>http://xianmuai.s7.tunnelfrp.com/search-arena/?query=德馨&amp;page_size=40&amp;city=杭州</td>\n", "    </tr>\n", "    <tr>\n", "      <th>21348</th>\n", "      <td>袋子</td>\n", "      <td>46</td>\n", "      <td>48</td>\n", "      <td>504</td>\n", "      <td>6</td>\n", "      <td>0.011905</td>\n", "      <td>8</td>\n", "      <td>0.173913</td>\n", "      <td>17.5</td>\n", "      <td>42.75</td>\n", "      <td>55.0</td>\n", "      <td>中频搜索词</td>\n", "      <td>http://xianmuai.s7.tunnelfrp.com/search-arena/?query=袋子&amp;page_size=40&amp;city=杭州</td>\n", "    </tr>\n", "    <tr>\n", "      <th>18011</th>\n", "      <td>维</td>\n", "      <td>45</td>\n", "      <td>35</td>\n", "      <td>431</td>\n", "      <td>1</td>\n", "      <td>0.002320</td>\n", "      <td>2</td>\n", "      <td>0.044444</td>\n", "      <td>18.0</td>\n", "      <td>18.00</td>\n", "      <td>18.0</td>\n", "      <td>中频搜索词</td>\n", "      <td>http://xianmuai.s7.tunnelfrp.com/search-arena/?query=维&amp;page_size=40&amp;city=杭州</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16831</th>\n", "      <td>碧根果</td>\n", "      <td>43</td>\n", "      <td>42</td>\n", "      <td>445</td>\n", "      <td>3</td>\n", "      <td>0.006742</td>\n", "      <td>3</td>\n", "      <td>0.069767</td>\n", "      <td>18.0</td>\n", "      <td>18.00</td>\n", "      <td>18.0</td>\n", "      <td>中频搜索词</td>\n", "      <td>http://xianmuai.s7.tunnelfrp.com/search-arena/?query=碧根果&amp;page_size=40&amp;city=杭州</td>\n", "    </tr>\n", "    <tr>\n", "      <th>15085</th>\n", "      <td>片油</td>\n", "      <td>42</td>\n", "      <td>41</td>\n", "      <td>481</td>\n", "      <td>3</td>\n", "      <td>0.006237</td>\n", "      <td>4</td>\n", "      <td>0.095238</td>\n", "      <td>18.0</td>\n", "      <td>18.50</td>\n", "      <td>18.8</td>\n", "      <td>中频搜索词</td>\n", "      <td>http://xianmuai.s7.tunnelfrp.com/search-arena/?query=片油&amp;page_size=40&amp;city=杭州</td>\n", "    </tr>\n", "    <tr>\n", "      <th>19364</th>\n", "      <td>芝士奶酪</td>\n", "      <td>41</td>\n", "      <td>37</td>\n", "      <td>824</td>\n", "      <td>19</td>\n", "      <td>0.023058</td>\n", "      <td>15</td>\n", "      <td>0.365854</td>\n", "      <td>25.0</td>\n", "      <td>40.00</td>\n", "      <td>58.8</td>\n", "      <td>中频搜索词</td>\n", "      <td>http://xianmuai.s7.tunnelfrp.com/search-arena/?query=芝士奶酪&amp;page_size=40&amp;city=杭州</td>\n", "    </tr>\n", "    <tr>\n", "      <th>18962</th>\n", "      <td>色拉油</td>\n", "      <td>41</td>\n", "      <td>40</td>\n", "      <td>377</td>\n", "      <td>1</td>\n", "      <td>0.002653</td>\n", "      <td>4</td>\n", "      <td>0.097561</td>\n", "      <td>27.0</td>\n", "      <td>27.00</td>\n", "      <td>27.0</td>\n", "      <td>中频搜索词</td>\n", "      <td>http://xianmuai.s7.tunnelfrp.com/search-arena/?query=色拉油&amp;page_size=40&amp;city=杭州</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16176</th>\n", "      <td>白柚</td>\n", "      <td>40</td>\n", "      <td>36</td>\n", "      <td>712</td>\n", "      <td>3</td>\n", "      <td>0.004213</td>\n", "      <td>4</td>\n", "      <td>0.100000</td>\n", "      <td>19.0</td>\n", "      <td>19.00</td>\n", "      <td>19.0</td>\n", "      <td>中频搜索词</td>\n", "      <td>http://xianmuai.s7.tunnelfrp.com/search-arena/?query=白柚&amp;page_size=40&amp;city=杭州</td>\n", "    </tr>\n", "    <tr>\n", "      <th>18967</th>\n", "      <td>色素</td>\n", "      <td>40</td>\n", "      <td>44</td>\n", "      <td>619</td>\n", "      <td>15</td>\n", "      <td>0.024233</td>\n", "      <td>10</td>\n", "      <td>0.250000</td>\n", "      <td>17.0</td>\n", "      <td>28.50</td>\n", "      <td>41.0</td>\n", "      <td>中频搜索词</td>\n", "      <td>http://xianmuai.s7.tunnelfrp.com/search-arena/?query=色素&amp;page_size=40&amp;city=杭州</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12303</th>\n", "      <td>椰丝</td>\n", "      <td>40</td>\n", "      <td>36</td>\n", "      <td>476</td>\n", "      <td>4</td>\n", "      <td>0.008403</td>\n", "      <td>5</td>\n", "      <td>0.125000</td>\n", "      <td>20.5</td>\n", "      <td>24.25</td>\n", "      <td>24.7</td>\n", "      <td>中频搜索词</td>\n", "      <td>http://xianmuai.s7.tunnelfrp.com/search-arena/?query=椰丝&amp;page_size=40&amp;city=杭州</td>\n", "    </tr>\n", "    <tr>\n", "      <th>20273</th>\n", "      <td>莳萝</td>\n", "      <td>40</td>\n", "      <td>51</td>\n", "      <td>340</td>\n", "      <td>6</td>\n", "      <td>0.017647</td>\n", "      <td>5</td>\n", "      <td>0.125000</td>\n", "      <td>14.5</td>\n", "      <td>42.75</td>\n", "      <td>55.0</td>\n", "      <td>中频搜索词</td>\n", "      <td>http://xianmuai.s7.tunnelfrp.com/search-arena/?query=莳萝&amp;page_size=40&amp;city=杭州</td>\n", "    </tr>\n", "    <tr>\n", "      <th>23027</th>\n", "      <td>阿达子</td>\n", "      <td>39</td>\n", "      <td>40</td>\n", "      <td>690</td>\n", "      <td>14</td>\n", "      <td>0.020290</td>\n", "      <td>5</td>\n", "      <td>0.128205</td>\n", "      <td>53.5</td>\n", "      <td>58.25</td>\n", "      <td>84.1</td>\n", "      <td>中频搜索词</td>\n", "      <td>http://xianmuai.s7.tunnelfrp.com/search-arena/?query=阿达子&amp;page_size=40&amp;city=杭州</td>\n", "    </tr>\n", "    <tr>\n", "      <th>19441</th>\n", "      <td>芝士蛋糕</td>\n", "      <td>39</td>\n", "      <td>49</td>\n", "      <td>1766</td>\n", "      <td>73</td>\n", "      <td>0.041336</td>\n", "      <td>28</td>\n", "      <td>0.717949</td>\n", "      <td>17.5</td>\n", "      <td>43.25</td>\n", "      <td>131.9</td>\n", "      <td>中频搜索词</td>\n", "      <td>http://xianmuai.s7.tunnelfrp.com/search-arena/?query=芝士蛋糕&amp;page_size=40&amp;city=杭州</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10741</th>\n", "      <td>无菌蛋</td>\n", "      <td>38</td>\n", "      <td>33</td>\n", "      <td>369</td>\n", "      <td>5</td>\n", "      <td>0.013550</td>\n", "      <td>2</td>\n", "      <td>0.052632</td>\n", "      <td>58.0</td>\n", "      <td>79.00</td>\n", "      <td>103.0</td>\n", "      <td>中频搜索词</td>\n", "      <td>http://xianmuai.s7.tunnelfrp.com/search-arena/?query=无菌蛋&amp;page_size=40&amp;city=杭州</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11432</th>\n", "      <td>果浆</td>\n", "      <td>37</td>\n", "      <td>44</td>\n", "      <td>1164</td>\n", "      <td>39</td>\n", "      <td>0.033505</td>\n", "      <td>17</td>\n", "      <td>0.459459</td>\n", "      <td>30.5</td>\n", "      <td>47.25</td>\n", "      <td>123.0</td>\n", "      <td>中频搜索词</td>\n", "      <td>http://xianmuai.s7.tunnelfrp.com/search-arena/?query=果浆&amp;page_size=40&amp;city=杭州</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14962</th>\n", "      <td>爱氏</td>\n", "      <td>37</td>\n", "      <td>36</td>\n", "      <td>218</td>\n", "      <td>5</td>\n", "      <td>0.022936</td>\n", "      <td>4</td>\n", "      <td>0.108108</td>\n", "      <td>17.0</td>\n", "      <td>18.00</td>\n", "      <td>21.0</td>\n", "      <td>中频搜索词</td>\n", "      <td>http://xianmuai.s7.tunnelfrp.com/search-arena/?query=爱氏&amp;page_size=40&amp;city=杭州</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12057</th>\n", "      <td>桃汁</td>\n", "      <td>36</td>\n", "      <td>31</td>\n", "      <td>505</td>\n", "      <td>9</td>\n", "      <td>0.017822</td>\n", "      <td>7</td>\n", "      <td>0.194444</td>\n", "      <td>28.0</td>\n", "      <td>38.00</td>\n", "      <td>39.0</td>\n", "      <td>中频搜索词</td>\n", "      <td>http://xianmuai.s7.tunnelfrp.com/search-arena/?query=桃汁&amp;page_size=40&amp;city=杭州</td>\n", "    </tr>\n", "    <tr>\n", "      <th>19147</th>\n", "      <td>芒果丁</td>\n", "      <td>35</td>\n", "      <td>38</td>\n", "      <td>872</td>\n", "      <td>22</td>\n", "      <td>0.025229</td>\n", "      <td>16</td>\n", "      <td>0.457143</td>\n", "      <td>26.5</td>\n", "      <td>28.00</td>\n", "      <td>30.0</td>\n", "      <td>中频搜索词</td>\n", "      <td>http://xianmuai.s7.tunnelfrp.com/search-arena/?query=芒果丁&amp;page_size=40&amp;city=杭州</td>\n", "    </tr>\n", "    <tr>\n", "      <th>21024</th>\n", "      <td>蛋清</td>\n", "      <td>35</td>\n", "      <td>31</td>\n", "      <td>379</td>\n", "      <td>2</td>\n", "      <td>0.005277</td>\n", "      <td>3</td>\n", "      <td>0.085714</td>\n", "      <td>59.5</td>\n", "      <td>88.75</td>\n", "      <td>106.3</td>\n", "      <td>中频搜索词</td>\n", "      <td>http://xianmuai.s7.tunnelfrp.com/search-arena/?query=蛋清&amp;page_size=40&amp;city=杭州</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13708</th>\n", "      <td>泰式红茶</td>\n", "      <td>34</td>\n", "      <td>33</td>\n", "      <td>262</td>\n", "      <td>3</td>\n", "      <td>0.011450</td>\n", "      <td>4</td>\n", "      <td>0.117647</td>\n", "      <td>18.0</td>\n", "      <td>21.00</td>\n", "      <td>22.8</td>\n", "      <td>中频搜索词</td>\n", "      <td>http://xianmuai.s7.tunnelfrp.com/search-arena/?query=泰式红茶&amp;page_size=40&amp;city=杭州</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6968</th>\n", "      <td>奶油枪</td>\n", "      <td>34</td>\n", "      <td>23</td>\n", "      <td>765</td>\n", "      <td>10</td>\n", "      <td>0.013072</td>\n", "      <td>7</td>\n", "      <td>0.205882</td>\n", "      <td>32.5</td>\n", "      <td>41.25</td>\n", "      <td>68.2</td>\n", "      <td>中频搜索词</td>\n", "      <td>http://xianmuai.s7.tunnelfrp.com/search-arena/?query=奶油枪&amp;page_size=40&amp;city=杭州</td>\n", "    </tr>\n", "    <tr>\n", "      <th>17150</th>\n", "      <td>米乳</td>\n", "      <td>33</td>\n", "      <td>33</td>\n", "      <td>469</td>\n", "      <td>7</td>\n", "      <td>0.014925</td>\n", "      <td>8</td>\n", "      <td>0.242424</td>\n", "      <td>18.0</td>\n", "      <td>22.50</td>\n", "      <td>30.2</td>\n", "      <td>中频搜索词</td>\n", "      <td>http://xianmuai.s7.tunnelfrp.com/search-arena/?query=米乳&amp;page_size=40&amp;city=杭州</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7076</th>\n", "      <td>奶茶</td>\n", "      <td>32</td>\n", "      <td>28</td>\n", "      <td>619</td>\n", "      <td>9</td>\n", "      <td>0.014540</td>\n", "      <td>7</td>\n", "      <td>0.218750</td>\n", "      <td>17.0</td>\n", "      <td>18.00</td>\n", "      <td>34.6</td>\n", "      <td>中频搜索词</td>\n", "      <td>http://xianmuai.s7.tunnelfrp.com/search-arena/?query=奶茶&amp;page_size=40&amp;city=杭州</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16640</th>\n", "      <td>益力多</td>\n", "      <td>32</td>\n", "      <td>38</td>\n", "      <td>575</td>\n", "      <td>7</td>\n", "      <td>0.012174</td>\n", "      <td>5</td>\n", "      <td>0.156250</td>\n", "      <td>37.0</td>\n", "      <td>40.50</td>\n", "      <td>44.0</td>\n", "      <td>中频搜索词</td>\n", "      <td>http://xianmuai.s7.tunnelfrp.com/search-arena/?query=益力多&amp;page_size=40&amp;city=杭州</td>\n", "    </tr>\n", "    <tr>\n", "      <th>15454</th>\n", "      <td>猪油</td>\n", "      <td>31</td>\n", "      <td>43</td>\n", "      <td>354</td>\n", "      <td>3</td>\n", "      <td>0.008475</td>\n", "      <td>3</td>\n", "      <td>0.096774</td>\n", "      <td>27.0</td>\n", "      <td>34.50</td>\n", "      <td>39.0</td>\n", "      <td>中频搜索词</td>\n", "      <td>http://xianmuai.s7.tunnelfrp.com/search-arena/?query=猪油&amp;page_size=40&amp;city=杭州</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["      query  searched_users  searched_times  impression_cnt  click_cnt  \\\n", "5803   喷射奶油             204             207            5181         82   \n", "18287    罐头             150             200            5701        197   \n", "3629     冷冻             131             129            2904        138   \n", "15498   玉米油             129             121            1393         10   \n", "11419    果汁             124             131            3914         73   \n", "5046     可颂             112             116            2085         33   \n", "20457   葡萄干             109             111            1575         15   \n", "20905  薄荷糖浆             101             107            2223         52   \n", "17185     粉              99             107            2821         76   \n", "23826    饮料              94             110            3187         59   \n", "16108   番茄酱              85              97            1519         20   \n", "20985     蛋              85              79             767          9   \n", "16361   白黄油              83              86            1652         22   \n", "22063    达川              75              80             584         14   \n", "22368     酱              68              67            1676         35   \n", "23335    青团              66              71             903          6   \n", "21098   蛋糕盒              62              54             864         17   \n", "13240   汤力水              59              63             796          6   \n", "18406    美枚              52              56             286          2   \n", "8857     巴乐              50              50             333          2   \n", "21347     袋              50              51             832         16   \n", "11272  杨梅果酱              50              57            1280         23   \n", "3611     冰袋              49              44             617         13   \n", "3075    保鲜膜              47              47             387          2   \n", "9865   慕斯蛋糕              46              43            2301         79   \n", "9557     德馨              46              42             879         19   \n", "21348    袋<PERSON>              46              48             504          6   \n", "18011     维              45              35             431          1   \n", "16831   碧根果              43              42             445          3   \n", "15085    片油              42              41             481          3   \n", "19364  芝士奶酪              41              37             824         19   \n", "18962   色拉油              41              40             377          1   \n", "16176    白柚              40              36             712          3   \n", "18967    色素              40              44             619         15   \n", "12303    椰丝              40              36             476          4   \n", "20273    莳萝              40              51             340          6   \n", "23027   阿达子              39              40             690         14   \n", "19441  芝士蛋糕              39              49            1766         73   \n", "10741   无菌蛋              38              33             369          5   \n", "11432    果浆              37              44            1164         39   \n", "14962    爱氏              37              36             218          5   \n", "12057    桃汁              36              31             505          9   \n", "19147   芒果丁              35              38             872         22   \n", "21024    蛋清              35              31             379          2   \n", "13708  泰式红茶              34              33             262          3   \n", "6968    奶油枪              34              23             765         10   \n", "17150    米乳              33              33             469          7   \n", "7076     奶茶              32              28             619          9   \n", "16640   益力多              32              38             575          7   \n", "15454    猪油              31              43             354          3   \n", "\n", "        sku_ctr  clicked_user_cnt  user_ctr  p50_click_index  p75_click_index  \\\n", "5803   0.015827                58  0.284314             33.0            59.75   \n", "18287  0.034555                81  0.540000             21.0            32.75   \n", "3629   0.047521                62  0.473282             17.5            40.50   \n", "15498  0.007179                12  0.093023             18.0            18.00   \n", "11419  0.018651                49  0.395161             13.0            37.00   \n", "5046   0.015827                28  0.250000             18.0            36.00   \n", "20457  0.009524                20  0.183486             21.0            32.00   \n", "20905  0.023392                34  0.336634             20.0            26.75   \n", "17185  0.026941                45  0.454545             17.0            28.00   \n", "23826  0.018513                40  0.425532             35.0            72.00   \n", "16108  0.013167                17  0.200000             21.0            30.50   \n", "20985  0.011734                10  0.117647             19.0            24.00   \n", "16361  0.013317                25  0.301205             18.5            27.75   \n", "22063  0.023973                13  0.173333             17.5            18.75   \n", "22368  0.020883                18  0.264706             15.0            53.50   \n", "23335  0.006645                 6  0.090909             34.5            53.25   \n", "21098  0.019676                14  0.225806             18.0            82.00   \n", "13240  0.007538                 7  0.118644             18.0            18.00   \n", "18406  0.006993                 3  0.057692             18.5            18.75   \n", "8857   0.006006                 4  0.080000             28.5            33.25   \n", "21347  0.019231                13  0.260000             15.5            43.25   \n", "11272  0.017969                14  0.280000              6.0            42.50   \n", "3611   0.021070                 6  0.122449             19.0            26.00   \n", "3075   0.005168                 4  0.085106             18.5            18.75   \n", "9865   0.034333                27  0.586957             49.0            67.50   \n", "9557   0.021615                16  0.347826             18.0            40.00   \n", "21348  0.011905                 8  0.173913             17.5            42.75   \n", "18011  0.002320                 2  0.044444             18.0            18.00   \n", "16831  0.006742                 3  0.069767             18.0            18.00   \n", "15085  0.006237                 4  0.095238             18.0            18.50   \n", "19364  0.023058                15  0.365854             25.0            40.00   \n", "18962  0.002653                 4  0.097561             27.0            27.00   \n", "16176  0.004213                 4  0.100000             19.0            19.00   \n", "18967  0.024233                10  0.250000             17.0            28.50   \n", "12303  0.008403                 5  0.125000             20.5            24.25   \n", "20273  0.017647                 5  0.125000             14.5            42.75   \n", "23027  0.020290                 5  0.128205             53.5            58.25   \n", "19441  0.041336                28  0.717949             17.5            43.25   \n", "10741  0.013550                 2  0.052632             58.0            79.00   \n", "11432  0.033505                17  0.459459             30.5            47.25   \n", "14962  0.022936                 4  0.108108             17.0            18.00   \n", "12057  0.017822                 7  0.194444             28.0            38.00   \n", "19147  0.025229                16  0.457143             26.5            28.00   \n", "21024  0.005277                 3  0.085714             59.5            88.75   \n", "13708  0.011450                 4  0.117647             18.0            21.00   \n", "6968   0.013072                 7  0.205882             32.5            41.25   \n", "17150  0.014925                 8  0.242424             18.0            22.50   \n", "7076   0.014540                 7  0.218750             17.0            18.00   \n", "16640  0.012174                 5  0.156250             37.0            40.50   \n", "15454  0.008475                 3  0.096774             27.0            34.50   \n", "\n", "       p90_click_index 搜索频次标签  \\\n", "5803              85.9  中频搜索词   \n", "18287             47.0  中频搜索词   \n", "3629              79.9  中频搜索词   \n", "15498             20.9  中频搜索词   \n", "11419             73.4  中频搜索词   \n", "5046              62.8  中频搜索词   \n", "20457             58.6  中频搜索词   \n", "20905             41.0  中频搜索词   \n", "17185             48.6  中频搜索词   \n", "23826            142.2  中频搜索词   \n", "16108             39.0  中频搜索词   \n", "20985             27.4  中频搜索词   \n", "16361             38.0  中频搜索词   \n", "22063             35.7  中频搜索词   \n", "22368             68.6  中频搜索词   \n", "23335             60.5  中频搜索词   \n", "21098            126.0  中频搜索词   \n", "13240             33.0  中频搜索词   \n", "18406             18.9  中频搜索词   \n", "8857              36.1  中频搜索词   \n", "21347             69.5  中频搜索词   \n", "11272             54.0  中频搜索词   \n", "3611              27.0  中频搜索词   \n", "3075              18.9  中频搜索词   \n", "9865             105.0  中频搜索词   \n", "9557              50.6  中频搜索词   \n", "21348             55.0  中频搜索词   \n", "18011             18.0  中频搜索词   \n", "16831             18.0  中频搜索词   \n", "15085             18.8  中频搜索词   \n", "19364             58.8  中频搜索词   \n", "18962             27.0  中频搜索词   \n", "16176             19.0  中频搜索词   \n", "18967             41.0  中频搜索词   \n", "12303             24.7  中频搜索词   \n", "20273             55.0  中频搜索词   \n", "23027             84.1  中频搜索词   \n", "19441            131.9  中频搜索词   \n", "10741            103.0  中频搜索词   \n", "11432            123.0  中频搜索词   \n", "14962             21.0  中频搜索词   \n", "12057             39.0  中频搜索词   \n", "19147             30.0  中频搜索词   \n", "21024            106.3  中频搜索词   \n", "13708             22.8  中频搜索词   \n", "6968              68.2  中频搜索词   \n", "17150             30.2  中频搜索词   \n", "7076              34.6  中频搜索词   \n", "16640             44.0  中频搜索词   \n", "15454             39.0  中频搜索词   \n", "\n", "                                                                                 link  \n", "5803   http://xianmuai.s7.tunnelfrp.com/search-arena/?query=喷射奶油&page_size=40&city=杭州  \n", "18287    http://xianmuai.s7.tunnelfrp.com/search-arena/?query=罐头&page_size=40&city=杭州  \n", "3629     http://xianmuai.s7.tunnelfrp.com/search-arena/?query=冷冻&page_size=40&city=杭州  \n", "15498   http://xianmuai.s7.tunnelfrp.com/search-arena/?query=玉米油&page_size=40&city=杭州  \n", "11419    http://xianmuai.s7.tunnelfrp.com/search-arena/?query=果汁&page_size=40&city=杭州  \n", "5046     http://xianmuai.s7.tunnelfrp.com/search-arena/?query=可颂&page_size=40&city=杭州  \n", "20457   http://xianmuai.s7.tunnelfrp.com/search-arena/?query=葡萄干&page_size=40&city=杭州  \n", "20905  http://xianmuai.s7.tunnelfrp.com/search-arena/?query=薄荷糖浆&page_size=40&city=杭州  \n", "17185     http://xianmuai.s7.tunnelfrp.com/search-arena/?query=粉&page_size=40&city=杭州  \n", "23826    http://xianmuai.s7.tunnelfrp.com/search-arena/?query=饮料&page_size=40&city=杭州  \n", "16108   http://xianmuai.s7.tunnelfrp.com/search-arena/?query=番茄酱&page_size=40&city=杭州  \n", "20985     http://xianmuai.s7.tunnelfrp.com/search-arena/?query=蛋&page_size=40&city=杭州  \n", "16361   http://xianmuai.s7.tunnelfrp.com/search-arena/?query=白黄油&page_size=40&city=杭州  \n", "22063    http://xianmuai.s7.tunnelfrp.com/search-arena/?query=达川&page_size=40&city=杭州  \n", "22368     http://xianmuai.s7.tunnelfrp.com/search-arena/?query=酱&page_size=40&city=杭州  \n", "23335    http://xianmuai.s7.tunnelfrp.com/search-arena/?query=青团&page_size=40&city=杭州  \n", "21098   http://xianmuai.s7.tunnelfrp.com/search-arena/?query=蛋糕盒&page_size=40&city=杭州  \n", "13240   http://xianmuai.s7.tunnelfrp.com/search-arena/?query=汤力水&page_size=40&city=杭州  \n", "18406    http://xianmuai.s7.tunnelfrp.com/search-arena/?query=美枚&page_size=40&city=杭州  \n", "8857     http://xianmuai.s7.tunnelfrp.com/search-arena/?query=巴乐&page_size=40&city=杭州  \n", "21347     http://xianmuai.s7.tunnelfrp.com/search-arena/?query=袋&page_size=40&city=杭州  \n", "11272  http://xianmuai.s7.tunnelfrp.com/search-arena/?query=杨梅果酱&page_size=40&city=杭州  \n", "3611     http://xianmuai.s7.tunnelfrp.com/search-arena/?query=冰袋&page_size=40&city=杭州  \n", "3075    http://xianmuai.s7.tunnelfrp.com/search-arena/?query=保鲜膜&page_size=40&city=杭州  \n", "9865   http://xianmuai.s7.tunnelfrp.com/search-arena/?query=慕斯蛋糕&page_size=40&city=杭州  \n", "9557     http://xianmuai.s7.tunnelfrp.com/search-arena/?query=德馨&page_size=40&city=杭州  \n", "21348    http://xianmuai.s7.tunnelfrp.com/search-arena/?query=袋子&page_size=40&city=杭州  \n", "18011     http://xianmuai.s7.tunnelfrp.com/search-arena/?query=维&page_size=40&city=杭州  \n", "16831   http://xianmuai.s7.tunnelfrp.com/search-arena/?query=碧根果&page_size=40&city=杭州  \n", "15085    http://xianmuai.s7.tunnelfrp.com/search-arena/?query=片油&page_size=40&city=杭州  \n", "19364  http://xianmuai.s7.tunnelfrp.com/search-arena/?query=芝士奶酪&page_size=40&city=杭州  \n", "18962   http://xianmuai.s7.tunnelfrp.com/search-arena/?query=色拉油&page_size=40&city=杭州  \n", "16176    http://xianmuai.s7.tunnelfrp.com/search-arena/?query=白柚&page_size=40&city=杭州  \n", "18967    http://xianmuai.s7.tunnelfrp.com/search-arena/?query=色素&page_size=40&city=杭州  \n", "12303    http://xianmuai.s7.tunnelfrp.com/search-arena/?query=椰丝&page_size=40&city=杭州  \n", "20273    http://xianmuai.s7.tunnelfrp.com/search-arena/?query=莳萝&page_size=40&city=杭州  \n", "23027   http://xianmuai.s7.tunnelfrp.com/search-arena/?query=阿达子&page_size=40&city=杭州  \n", "19441  http://xianmuai.s7.tunnelfrp.com/search-arena/?query=芝士蛋糕&page_size=40&city=杭州  \n", "10741   http://xianmuai.s7.tunnelfrp.com/search-arena/?query=无菌蛋&page_size=40&city=杭州  \n", "11432    http://xianmuai.s7.tunnelfrp.com/search-arena/?query=果浆&page_size=40&city=杭州  \n", "14962    http://xianmuai.s7.tunnelfrp.com/search-arena/?query=爱氏&page_size=40&city=杭州  \n", "12057    http://xianmuai.s7.tunnelfrp.com/search-arena/?query=桃汁&page_size=40&city=杭州  \n", "19147   http://xianmuai.s7.tunnelfrp.com/search-arena/?query=芒果丁&page_size=40&city=杭州  \n", "21024    http://xianmuai.s7.tunnelfrp.com/search-arena/?query=蛋清&page_size=40&city=杭州  \n", "13708  http://xianmuai.s7.tunnelfrp.com/search-arena/?query=泰式红茶&page_size=40&city=杭州  \n", "6968    http://xianmuai.s7.tunnelfrp.com/search-arena/?query=奶油枪&page_size=40&city=杭州  \n", "17150    http://xianmuai.s7.tunnelfrp.com/search-arena/?query=米乳&page_size=40&city=杭州  \n", "7076     http://xianmuai.s7.tunnelfrp.com/search-arena/?query=奶茶&page_size=40&city=杭州  \n", "16640   http://xianmuai.s7.tunnelfrp.com/search-arena/?query=益力多&page_size=40&city=杭州  \n", "15454    http://xianmuai.s7.tunnelfrp.com/search-arena/?query=猪油&page_size=40&city=杭州  "]}, "execution_count": 22, "metadata": {}, "output_type": "execute_result"}], "source": ["poor_medium_frequency_query_df = medium_df[\n", "    (medium_df[\"p50_click_index\"] >= 17.0)\n", "    | (medium_df[\"p75_click_index\"] > 35)\n", "]\n", "\n", "poor_medium_frequency_query_df=poor_medium_frequency_query_df.sort_values(by=\"searched_users\", ascending=False)\n", "poor_medium_frequency_query_df[\"link\"]=poor_medium_frequency_query_df['query'].apply(lambda x: f\"http://xianmuai.s7.tunnelfrp.com/search-arena/?query={x}&page_size=40&city=杭州\")\n", "poor_medium_frequency_query_df.head(50)"]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>searched_users</th>\n", "      <th>searched_times</th>\n", "      <th>impression_cnt</th>\n", "      <th>click_cnt</th>\n", "      <th>sku_ctr</th>\n", "      <th>clicked_user_cnt</th>\n", "      <th>user_ctr</th>\n", "      <th>p50_click_index</th>\n", "      <th>p75_click_index</th>\n", "      <th>p90_click_index</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>count</th>\n", "      <td>20388.000000</td>\n", "      <td>20388.000000</td>\n", "      <td>20388.000000</td>\n", "      <td>20388.000000</td>\n", "      <td>20280.000000</td>\n", "      <td>20388.000000</td>\n", "      <td>20280.000000</td>\n", "      <td>9409.00000</td>\n", "      <td>9409.000000</td>\n", "      <td>9409.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>mean</th>\n", "      <td>1.643957</td>\n", "      <td>1.971405</td>\n", "      <td>23.516922</td>\n", "      <td>1.010987</td>\n", "      <td>0.054787</td>\n", "      <td>0.637973</td>\n", "      <td>0.377872</td>\n", "      <td>7.57174</td>\n", "      <td>9.099612</td>\n", "      <td>10.255713</td>\n", "    </tr>\n", "    <tr>\n", "      <th>std</th>\n", "      <td>1.381802</td>\n", "      <td>1.735323</td>\n", "      <td>33.747929</td>\n", "      <td>1.736245</td>\n", "      <td>0.099277</td>\n", "      <td>0.899602</td>\n", "      <td>0.450574</td>\n", "      <td>12.84239</td>\n", "      <td>15.539110</td>\n", "      <td>17.810356</td>\n", "    </tr>\n", "    <tr>\n", "      <th>min</th>\n", "      <td>1.000000</td>\n", "      <td>1.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.00000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>25%</th>\n", "      <td>1.000000</td>\n", "      <td>1.000000</td>\n", "      <td>5.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>1.00000</td>\n", "      <td>1.000000</td>\n", "      <td>1.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>50%</th>\n", "      <td>1.000000</td>\n", "      <td>1.000000</td>\n", "      <td>12.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>3.00000</td>\n", "      <td>4.000000</td>\n", "      <td>4.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>75%</th>\n", "      <td>2.000000</td>\n", "      <td>2.000000</td>\n", "      <td>27.000000</td>\n", "      <td>1.000000</td>\n", "      <td>0.080000</td>\n", "      <td>1.000000</td>\n", "      <td>1.000000</td>\n", "      <td>10.00000</td>\n", "      <td>11.000000</td>\n", "      <td>12.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>max</th>\n", "      <td>12.000000</td>\n", "      <td>9.000000</td>\n", "      <td>528.000000</td>\n", "      <td>25.000000</td>\n", "      <td>3.000000</td>\n", "      <td>9.000000</td>\n", "      <td>2.000000</td>\n", "      <td>192.50000</td>\n", "      <td>239.000000</td>\n", "      <td>269.600000</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["       searched_users  searched_times  impression_cnt     click_cnt  \\\n", "count    20388.000000    20388.000000    20388.000000  20388.000000   \n", "mean         1.643957        1.971405       23.516922      1.010987   \n", "std          1.381802        1.735323       33.747929      1.736245   \n", "min          1.000000        1.000000        0.000000      0.000000   \n", "25%          1.000000        1.000000        5.000000      0.000000   \n", "50%          1.000000        1.000000       12.000000      0.000000   \n", "75%          2.000000        2.000000       27.000000      1.000000   \n", "max         12.000000        9.000000      528.000000     25.000000   \n", "\n", "            sku_ctr  clicked_user_cnt      user_ctr  p50_click_index  \\\n", "count  20280.000000      20388.000000  20280.000000       9409.00000   \n", "mean       0.054787          0.637973      0.377872          7.57174   \n", "std        0.099277          0.899602      0.450574         12.84239   \n", "min        0.000000          0.000000      0.000000          0.00000   \n", "25%        0.000000          0.000000      0.000000          1.00000   \n", "50%        0.000000          0.000000      0.000000          3.00000   \n", "75%        0.080000          1.000000      1.000000         10.00000   \n", "max        3.000000          9.000000      2.000000        192.50000   \n", "\n", "       p75_click_index  p90_click_index  \n", "count      9409.000000      9409.000000  \n", "mean          9.099612        10.255713  \n", "std          15.539110        17.810356  \n", "min           0.000000         0.000000  \n", "25%           1.000000         1.000000  \n", "50%           4.000000         4.000000  \n", "75%          11.000000        12.000000  \n", "max         239.000000       269.600000  "]}, "execution_count": 21, "metadata": {}, "output_type": "execute_result"}], "source": ["low_frequency_df = top_query_labeled_df[top_query_labeled_df[\"搜索频次标签\"] == \"低频搜索词(<10)\"]\n", "low_frequency_df.describe()"]}, {"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["       searched_users  searched_times  impression_cnt  click_cnt  sku_ctr  \\\n", "count    10978.000000    10978.000000    10978.000000    10978.0  10978.0   \n", "mean         1.259701        1.356258       13.694935        0.0      0.0   \n", "std          0.787797        0.895754       16.692099        0.0      0.0   \n", "min          1.000000        1.000000        1.000000        0.0      0.0   \n", "25%          1.000000        1.000000        4.000000        0.0      0.0   \n", "50%          1.000000        1.000000        8.000000        0.0      0.0   \n", "75%          1.000000        1.000000       16.000000        0.0      0.0   \n", "max         10.000000        9.000000      271.000000        0.0      0.0   \n", "\n", "       clicked_user_cnt  user_ctr  p50_click_index  p75_click_index  \\\n", "count           10978.0   10978.0              0.0              0.0   \n", "mean                0.0       0.0              NaN              NaN   \n", "std                 0.0       0.0              NaN              NaN   \n", "min                 0.0       0.0              NaN              NaN   \n", "25%                 0.0       0.0              NaN              NaN   \n", "50%                 0.0       0.0              NaN              NaN   \n", "75%                 0.0       0.0              NaN              NaN   \n", "max                 0.0       0.0              NaN              NaN   \n", "\n", "       p90_click_index  \n", "count              0.0  \n", "mean               NaN  \n", "std                NaN  \n", "min                <PERSON><PERSON>  \n", "25%                NaN  \n", "50%                NaN  \n", "75%                NaN  \n", "max                <PERSON>  \n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>query</th>\n", "      <th>searched_users</th>\n", "      <th>searched_times</th>\n", "      <th>impression_cnt</th>\n", "      <th>click_cnt</th>\n", "      <th>sku_ctr</th>\n", "      <th>clicked_user_cnt</th>\n", "      <th>user_ctr</th>\n", "      <th>搜索频次标签</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>22804</th>\n", "      <td>鲜沐农场</td>\n", "      <td>9</td>\n", "      <td>9</td>\n", "      <td>72</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>低频搜索词(&lt;10)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3876</th>\n", "      <td>卡士酸奶</td>\n", "      <td>9</td>\n", "      <td>9</td>\n", "      <td>71</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>低频搜索词(&lt;10)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4498</th>\n", "      <td>名派</td>\n", "      <td>9</td>\n", "      <td>9</td>\n", "      <td>46</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>低频搜索词(&lt;10)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>511</th>\n", "      <td>Lori</td>\n", "      <td>9</td>\n", "      <td>9</td>\n", "      <td>35</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>低频搜索词(&lt;10)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>23175</th>\n", "      <td>黄天鹅</td>\n", "      <td>8</td>\n", "      <td>9</td>\n", "      <td>146</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>低频搜索词(&lt;10)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8420</th>\n", "      <td>干姜水</td>\n", "      <td>8</td>\n", "      <td>9</td>\n", "      <td>126</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>低频搜索词(&lt;10)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4600</th>\n", "      <td>听茶昵语糖纳红豆</td>\n", "      <td>8</td>\n", "      <td>9</td>\n", "      <td>110</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>低频搜索词(&lt;10)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1435</th>\n", "      <td>三元</td>\n", "      <td>8</td>\n", "      <td>9</td>\n", "      <td>92</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>低频搜索词(&lt;10)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12640</th>\n", "      <td>波霸</td>\n", "      <td>8</td>\n", "      <td>9</td>\n", "      <td>65</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>低频搜索词(&lt;10)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>689</th>\n", "      <td>T80</td>\n", "      <td>7</td>\n", "      <td>9</td>\n", "      <td>96</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>低频搜索词(&lt;10)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>21806</th>\n", "      <td>青木瓜</td>\n", "      <td>7</td>\n", "      <td>9</td>\n", "      <td>57</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>低频搜索词(&lt;10)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>20205</th>\n", "      <td>豆腐</td>\n", "      <td>4</td>\n", "      <td>9</td>\n", "      <td>47</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>低频搜索词(&lt;10)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14738</th>\n", "      <td>甘草酸梅汁</td>\n", "      <td>1</td>\n", "      <td>9</td>\n", "      <td>95</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>低频搜索词(&lt;10)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>15160</th>\n", "      <td>白象</td>\n", "      <td>10</td>\n", "      <td>8</td>\n", "      <td>94</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>低频搜索词(&lt;10)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7665</th>\n", "      <td>寒</td>\n", "      <td>9</td>\n", "      <td>8</td>\n", "      <td>95</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>低频搜索词(&lt;10)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>20777</th>\n", "      <td>配方</td>\n", "      <td>9</td>\n", "      <td>8</td>\n", "      <td>51</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>低频搜索词(&lt;10)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6374</th>\n", "      <td>奶茶杯</td>\n", "      <td>8</td>\n", "      <td>8</td>\n", "      <td>115</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>低频搜索词(&lt;10)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>15858</th>\n", "      <td>竹</td>\n", "      <td>8</td>\n", "      <td>8</td>\n", "      <td>102</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>低频搜索词(&lt;10)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9277</th>\n", "      <td>打蛋器</td>\n", "      <td>8</td>\n", "      <td>8</td>\n", "      <td>89</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>低频搜索词(&lt;10)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>20565</th>\n", "      <td>运费券</td>\n", "      <td>8</td>\n", "      <td>8</td>\n", "      <td>53</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>低频搜索词(&lt;10)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12058</th>\n", "      <td>毛豆</td>\n", "      <td>7</td>\n", "      <td>8</td>\n", "      <td>53</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>低频搜索词(&lt;10)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>18615</th>\n", "      <td>草莓蓝莓</td>\n", "      <td>7</td>\n", "      <td>8</td>\n", "      <td>39</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>低频搜索词(&lt;10)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9898</th>\n", "      <td>无水奶油</td>\n", "      <td>6</td>\n", "      <td>8</td>\n", "      <td>171</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>低频搜索词(&lt;10)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>15870</th>\n", "      <td>竹签</td>\n", "      <td>6</td>\n", "      <td>8</td>\n", "      <td>76</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>低频搜索词(&lt;10)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7742</th>\n", "      <td>小多肉</td>\n", "      <td>6</td>\n", "      <td>8</td>\n", "      <td>55</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>低频搜索词(&lt;10)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>22026</th>\n", "      <td>韩式</td>\n", "      <td>6</td>\n", "      <td>8</td>\n", "      <td>41</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>低频搜索词(&lt;10)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16245</th>\n", "      <td>紫薯泥</td>\n", "      <td>4</td>\n", "      <td>8</td>\n", "      <td>213</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>低频搜索词(&lt;10)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>21073</th>\n", "      <td>金煌芒果</td>\n", "      <td>4</td>\n", "      <td>8</td>\n", "      <td>80</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>低频搜索词(&lt;10)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>22739</th>\n", "      <td>鱼丸</td>\n", "      <td>2</td>\n", "      <td>8</td>\n", "      <td>45</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>低频搜索词(&lt;10)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16134</th>\n", "      <td>糖酱</td>\n", "      <td>8</td>\n", "      <td>7</td>\n", "      <td>156</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>低频搜索词(&lt;10)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>22363</th>\n", "      <td>香菜</td>\n", "      <td>8</td>\n", "      <td>7</td>\n", "      <td>122</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>低频搜索词(&lt;10)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12274</th>\n", "      <td>沃柑汁</td>\n", "      <td>7</td>\n", "      <td>7</td>\n", "      <td>118</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>低频搜索词(&lt;10)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9256</th>\n", "      <td>手标红茶</td>\n", "      <td>7</td>\n", "      <td>7</td>\n", "      <td>105</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>低频搜索词(&lt;10)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2794</th>\n", "      <td>六寸</td>\n", "      <td>7</td>\n", "      <td>7</td>\n", "      <td>77</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>低频搜索词(&lt;10)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12952</th>\n", "      <td>液态</td>\n", "      <td>7</td>\n", "      <td>7</td>\n", "      <td>54</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>低频搜索词(&lt;10)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6752</th>\n", "      <td>宇峰凉粉</td>\n", "      <td>7</td>\n", "      <td>7</td>\n", "      <td>45</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>低频搜索词(&lt;10)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>21001</th>\n", "      <td>金山</td>\n", "      <td>7</td>\n", "      <td>7</td>\n", "      <td>37</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>低频搜索词(&lt;10)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>23512</th>\n", "      <td>黑松露酱</td>\n", "      <td>7</td>\n", "      <td>7</td>\n", "      <td>36</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>低频搜索词(&lt;10)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6108</th>\n", "      <td>女神</td>\n", "      <td>7</td>\n", "      <td>7</td>\n", "      <td>7</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>低频搜索词(&lt;10)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12738</th>\n", "      <td>洛神花</td>\n", "      <td>6</td>\n", "      <td>7</td>\n", "      <td>143</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>低频搜索词(&lt;10)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7316</th>\n", "      <td>安德鲁1kg水果颗粒果酱系列</td>\n", "      <td>6</td>\n", "      <td>7</td>\n", "      <td>96</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>低频搜索词(&lt;10)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9466</th>\n", "      <td>拉线膏</td>\n", "      <td>6</td>\n", "      <td>7</td>\n", "      <td>82</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>低频搜索词(&lt;10)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10180</th>\n", "      <td>普利欧经典黑森林蛋糕</td>\n", "      <td>6</td>\n", "      <td>7</td>\n", "      <td>67</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>低频搜索词(&lt;10)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>18264</th>\n", "      <td>苏格私甜巧克力咸奶油蛋糕</td>\n", "      <td>6</td>\n", "      <td>7</td>\n", "      <td>49</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>低频搜索词(&lt;10)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3030</th>\n", "      <td>冰草</td>\n", "      <td>6</td>\n", "      <td>7</td>\n", "      <td>42</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>低频搜索词(&lt;10)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>18848</th>\n", "      <td>莫顿海盐薄片</td>\n", "      <td>6</td>\n", "      <td>7</td>\n", "      <td>33</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>低频搜索词(&lt;10)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>72</th>\n", "      <td>170</td>\n", "      <td>6</td>\n", "      <td>7</td>\n", "      <td>32</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>低频搜索词(&lt;10)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>18108</th>\n", "      <td>芦荟</td>\n", "      <td>6</td>\n", "      <td>7</td>\n", "      <td>16</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>低频搜索词(&lt;10)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>18768</th>\n", "      <td>莫奈花园纯牛奶</td>\n", "      <td>5</td>\n", "      <td>7</td>\n", "      <td>120</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>低频搜索词(&lt;10)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6458</th>\n", "      <td>奶香蜜</td>\n", "      <td>5</td>\n", "      <td>7</td>\n", "      <td>45</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>低频搜索词(&lt;10)</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                query  searched_users  searched_times  impression_cnt  \\\n", "22804            鲜沐农场               9               9              72   \n", "3876             卡士酸奶               9               9              71   \n", "4498               名派               9               9              46   \n", "511              Lori               9               9              35   \n", "23175             黄天鹅               8               9             146   \n", "8420              干姜水               8               9             126   \n", "4600         听茶昵语糖纳红豆               8               9             110   \n", "1435               三元               8               9              92   \n", "12640              波霸               8               9              65   \n", "689               T80               7               9              96   \n", "21806             青木瓜               7               9              57   \n", "20205              豆腐               4               9              47   \n", "14738           甘草酸梅汁               1               9              95   \n", "15160              白象              10               8              94   \n", "7665                寒               9               8              95   \n", "20777              配方               9               8              51   \n", "6374              奶茶杯               8               8             115   \n", "15858               竹               8               8             102   \n", "9277              打蛋器               8               8              89   \n", "20565             运费券               8               8              53   \n", "12058              毛豆               7               8              53   \n", "18615            草莓蓝莓               7               8              39   \n", "9898             无水奶油               6               8             171   \n", "15870              竹签               6               8              76   \n", "7742              小多肉               6               8              55   \n", "22026              韩式               6               8              41   \n", "16245             紫薯泥               4               8             213   \n", "21073            金煌芒果               4               8              80   \n", "22739              鱼丸               2               8              45   \n", "16134              糖酱               8               7             156   \n", "22363              香菜               8               7             122   \n", "12274             沃柑汁               7               7             118   \n", "9256             手标红茶               7               7             105   \n", "2794               六寸               7               7              77   \n", "12952              液态               7               7              54   \n", "6752             宇峰凉粉               7               7              45   \n", "21001              金山               7               7              37   \n", "23512            黑松露酱               7               7              36   \n", "6108               女神               7               7               7   \n", "12738             洛神花               6               7             143   \n", "7316   安德鲁1kg水果颗粒果酱系列               6               7              96   \n", "9466              拉线膏               6               7              82   \n", "10180      普利欧经典黑森林蛋糕               6               7              67   \n", "18264    苏格私甜巧克力咸奶油蛋糕               6               7              49   \n", "3030               冰草               6               7              42   \n", "18848          莫顿海盐薄片               6               7              33   \n", "72                170               6               7              32   \n", "18108              芦荟               6               7              16   \n", "18768         莫奈花园纯牛奶               5               7             120   \n", "6458              奶香蜜               5               7              45   \n", "\n", "       click_cnt  sku_ctr  clicked_user_cnt  user_ctr      搜索频次标签  \n", "22804          0      0.0                 0       0.0  低频搜索词(<10)  \n", "3876           0      0.0                 0       0.0  低频搜索词(<10)  \n", "4498           0      0.0                 0       0.0  低频搜索词(<10)  \n", "511            0      0.0                 0       0.0  低频搜索词(<10)  \n", "23175          0      0.0                 0       0.0  低频搜索词(<10)  \n", "8420           0      0.0                 0       0.0  低频搜索词(<10)  \n", "4600           0      0.0                 0       0.0  低频搜索词(<10)  \n", "1435           0      0.0                 0       0.0  低频搜索词(<10)  \n", "12640          0      0.0                 0       0.0  低频搜索词(<10)  \n", "689            0      0.0                 0       0.0  低频搜索词(<10)  \n", "21806          0      0.0                 0       0.0  低频搜索词(<10)  \n", "20205          0      0.0                 0       0.0  低频搜索词(<10)  \n", "14738          0      0.0                 0       0.0  低频搜索词(<10)  \n", "15160          0      0.0                 0       0.0  低频搜索词(<10)  \n", "7665           0      0.0                 0       0.0  低频搜索词(<10)  \n", "20777          0      0.0                 0       0.0  低频搜索词(<10)  \n", "6374           0      0.0                 0       0.0  低频搜索词(<10)  \n", "15858          0      0.0                 0       0.0  低频搜索词(<10)  \n", "9277           0      0.0                 0       0.0  低频搜索词(<10)  \n", "20565          0      0.0                 0       0.0  低频搜索词(<10)  \n", "12058          0      0.0                 0       0.0  低频搜索词(<10)  \n", "18615          0      0.0                 0       0.0  低频搜索词(<10)  \n", "9898           0      0.0                 0       0.0  低频搜索词(<10)  \n", "15870          0      0.0                 0       0.0  低频搜索词(<10)  \n", "7742           0      0.0                 0       0.0  低频搜索词(<10)  \n", "22026          0      0.0                 0       0.0  低频搜索词(<10)  \n", "16245          0      0.0                 0       0.0  低频搜索词(<10)  \n", "21073          0      0.0                 0       0.0  低频搜索词(<10)  \n", "22739          0      0.0                 0       0.0  低频搜索词(<10)  \n", "16134          0      0.0                 0       0.0  低频搜索词(<10)  \n", "22363          0      0.0                 0       0.0  低频搜索词(<10)  \n", "12274          0      0.0                 0       0.0  低频搜索词(<10)  \n", "9256           0      0.0                 0       0.0  低频搜索词(<10)  \n", "2794           0      0.0                 0       0.0  低频搜索词(<10)  \n", "12952          0      0.0                 0       0.0  低频搜索词(<10)  \n", "6752           0      0.0                 0       0.0  低频搜索词(<10)  \n", "21001          0      0.0                 0       0.0  低频搜索词(<10)  \n", "23512          0      0.0                 0       0.0  低频搜索词(<10)  \n", "6108           0      0.0                 0       0.0  低频搜索词(<10)  \n", "12738          0      0.0                 0       0.0  低频搜索词(<10)  \n", "7316           0      0.0                 0       0.0  低频搜索词(<10)  \n", "9466           0      0.0                 0       0.0  低频搜索词(<10)  \n", "10180          0      0.0                 0       0.0  低频搜索词(<10)  \n", "18264          0      0.0                 0       0.0  低频搜索词(<10)  \n", "3030           0      0.0                 0       0.0  低频搜索词(<10)  \n", "18848          0      0.0                 0       0.0  低频搜索词(<10)  \n", "72             0      0.0                 0       0.0  低频搜索词(<10)  \n", "18108          0      0.0                 0       0.0  低频搜索词(<10)  \n", "18768          0      0.0                 0       0.0  低频搜索词(<10)  \n", "6458           0      0.0                 0       0.0  低频搜索词(<10)  "]}, "execution_count": 22, "metadata": {}, "output_type": "execute_result"}], "source": ["print(low_frequency_df[low_frequency_df[\"user_ctr\"] < 0.01].describe())\n", "\n", "low_frequency_df[low_frequency_df[\"user_ctr\"] < 0.01].sort_values(\n", "    by=[\"searched_times\", \"searched_users\", \"impression_cnt\"], ascending=False\n", ")[\n", "    [\n", "        \"query\",\n", "        \"searched_users\",\n", "        \"searched_times\",\n", "        \"impression_cnt\",\n", "        \"click_cnt\",\n", "        \"sku_ctr\",\n", "        \"clicked_user_cnt\",\n", "        \"user_ctr\",\n", "        \"搜索频次标签\",\n", "    ]\n", "].head(\n", "    50\n", ")"]}, {"cell_type": "code", "execution_count": 23, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>query</th>\n", "      <th>searched_users</th>\n", "      <th>searched_times</th>\n", "      <th>impression_cnt</th>\n", "      <th>click_cnt</th>\n", "      <th>sku_ctr</th>\n", "      <th>clicked_user_cnt</th>\n", "      <th>user_ctr</th>\n", "      <th>p50_click_index</th>\n", "      <th>p75_click_index</th>\n", "      <th>p90_click_index</th>\n", "      <th>搜索频次标签</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>17756</th>\n", "      <td>芒果</td>\n", "      <td>7886</td>\n", "      <td>27021</td>\n", "      <td>307947</td>\n", "      <td>23861</td>\n", "      <td>0.077484</td>\n", "      <td>6273</td>\n", "      <td>0.795561</td>\n", "      <td>3.0</td>\n", "      <td>8.0</td>\n", "      <td>12.0</td>\n", "      <td>高频搜索词(&gt;230)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14027</th>\n", "      <td>牛奶</td>\n", "      <td>7292</td>\n", "      <td>12940</td>\n", "      <td>273657</td>\n", "      <td>15666</td>\n", "      <td>0.057247</td>\n", "      <td>5726</td>\n", "      <td>0.785460</td>\n", "      <td>9.0</td>\n", "      <td>19.0</td>\n", "      <td>26.0</td>\n", "      <td>高频搜索词(&gt;230)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6779</th>\n", "      <td>安佳</td>\n", "      <td>6380</td>\n", "      <td>15938</td>\n", "      <td>121436</td>\n", "      <td>11847</td>\n", "      <td>0.097558</td>\n", "      <td>4644</td>\n", "      <td>0.728471</td>\n", "      <td>1.0</td>\n", "      <td>4.0</td>\n", "      <td>7.0</td>\n", "      <td>高频搜索词(&gt;230)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>18481</th>\n", "      <td>草莓</td>\n", "      <td>6216</td>\n", "      <td>21333</td>\n", "      <td>236832</td>\n", "      <td>18117</td>\n", "      <td>0.076497</td>\n", "      <td>4674</td>\n", "      <td>0.752173</td>\n", "      <td>3.0</td>\n", "      <td>8.0</td>\n", "      <td>16.0</td>\n", "      <td>高频搜索词(&gt;230)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>19224</th>\n", "      <td>蓝莓</td>\n", "      <td>5581</td>\n", "      <td>16744</td>\n", "      <td>148786</td>\n", "      <td>11561</td>\n", "      <td>0.077702</td>\n", "      <td>4106</td>\n", "      <td>0.735842</td>\n", "      <td>2.0</td>\n", "      <td>5.0</td>\n", "      <td>10.0</td>\n", "      <td>高频搜索词(&gt;230)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6139</th>\n", "      <td>奶油</td>\n", "      <td>5211</td>\n", "      <td>9543</td>\n", "      <td>262185</td>\n", "      <td>10141</td>\n", "      <td>0.038679</td>\n", "      <td>3574</td>\n", "      <td>0.685857</td>\n", "      <td>12.0</td>\n", "      <td>30.0</td>\n", "      <td>50.0</td>\n", "      <td>高频搜索词(&gt;230)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10823</th>\n", "      <td>柠檬</td>\n", "      <td>5129</td>\n", "      <td>10508</td>\n", "      <td>157201</td>\n", "      <td>11487</td>\n", "      <td>0.073072</td>\n", "      <td>4035</td>\n", "      <td>0.786703</td>\n", "      <td>6.0</td>\n", "      <td>11.0</td>\n", "      <td>16.0</td>\n", "      <td>高频搜索词(&gt;230)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>23226</th>\n", "      <td>黄油</td>\n", "      <td>4608</td>\n", "      <td>8282</td>\n", "      <td>179807</td>\n", "      <td>6590</td>\n", "      <td>0.036650</td>\n", "      <td>2813</td>\n", "      <td>0.610593</td>\n", "      <td>10.0</td>\n", "      <td>22.0</td>\n", "      <td>31.0</td>\n", "      <td>高频搜索词(&gt;230)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7034</th>\n", "      <td>安佳淡奶油</td>\n", "      <td>4481</td>\n", "      <td>11399</td>\n", "      <td>74295</td>\n", "      <td>8167</td>\n", "      <td>0.109927</td>\n", "      <td>3469</td>\n", "      <td>0.776584</td>\n", "      <td>1.0</td>\n", "      <td>2.0</td>\n", "      <td>4.0</td>\n", "      <td>高频搜索词(&gt;230)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>21194</th>\n", "      <td>铁塔</td>\n", "      <td>3750</td>\n", "      <td>9705</td>\n", "      <td>45829</td>\n", "      <td>7200</td>\n", "      <td>0.157106</td>\n", "      <td>2831</td>\n", "      <td>0.756750</td>\n", "      <td>0.0</td>\n", "      <td>1.0</td>\n", "      <td>3.0</td>\n", "      <td>高频搜索词(&gt;230)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12968</th>\n", "      <td>淡奶油</td>\n", "      <td>2942</td>\n", "      <td>5705</td>\n", "      <td>137980</td>\n", "      <td>6176</td>\n", "      <td>0.044760</td>\n", "      <td>2250</td>\n", "      <td>0.764786</td>\n", "      <td>9.0</td>\n", "      <td>24.0</td>\n", "      <td>45.0</td>\n", "      <td>高频搜索词(&gt;230)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10993</th>\n", "      <td>树莓</td>\n", "      <td>2559</td>\n", "      <td>5772</td>\n", "      <td>45178</td>\n", "      <td>3387</td>\n", "      <td>0.074970</td>\n", "      <td>1559</td>\n", "      <td>0.610176</td>\n", "      <td>1.0</td>\n", "      <td>4.0</td>\n", "      <td>11.0</td>\n", "      <td>高频搜索词(&gt;230)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>22460</th>\n", "      <td>马斯卡彭</td>\n", "      <td>2461</td>\n", "      <td>5824</td>\n", "      <td>45516</td>\n", "      <td>4103</td>\n", "      <td>0.090144</td>\n", "      <td>1807</td>\n", "      <td>0.736649</td>\n", "      <td>0.0</td>\n", "      <td>2.0</td>\n", "      <td>6.0</td>\n", "      <td>高频搜索词(&gt;230)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13317</th>\n", "      <td>火龙果</td>\n", "      <td>2446</td>\n", "      <td>6591</td>\n", "      <td>62354</td>\n", "      <td>4847</td>\n", "      <td>0.077734</td>\n", "      <td>1876</td>\n", "      <td>0.767594</td>\n", "      <td>3.0</td>\n", "      <td>6.0</td>\n", "      <td>9.0</td>\n", "      <td>高频搜索词(&gt;230)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6391</th>\n", "      <td>奶酪</td>\n", "      <td>2444</td>\n", "      <td>4634</td>\n", "      <td>68030</td>\n", "      <td>3705</td>\n", "      <td>0.054461</td>\n", "      <td>1674</td>\n", "      <td>0.684943</td>\n", "      <td>4.0</td>\n", "      <td>10.0</td>\n", "      <td>22.0</td>\n", "      <td>高频搜索词(&gt;230)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>21755</th>\n", "      <td>青提</td>\n", "      <td>2423</td>\n", "      <td>6620</td>\n", "      <td>58250</td>\n", "      <td>4304</td>\n", "      <td>0.073888</td>\n", "      <td>1556</td>\n", "      <td>0.642444</td>\n", "      <td>1.0</td>\n", "      <td>4.0</td>\n", "      <td>10.0</td>\n", "      <td>高频搜索词(&gt;230)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5956</th>\n", "      <td>奥利奥</td>\n", "      <td>2383</td>\n", "      <td>3944</td>\n", "      <td>24469</td>\n", "      <td>3177</td>\n", "      <td>0.129838</td>\n", "      <td>1912</td>\n", "      <td>0.803699</td>\n", "      <td>1.0</td>\n", "      <td>2.0</td>\n", "      <td>4.0</td>\n", "      <td>高频搜索词(&gt;230)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14148</th>\n", "      <td>牛油果</td>\n", "      <td>2313</td>\n", "      <td>10785</td>\n", "      <td>87537</td>\n", "      <td>7604</td>\n", "      <td>0.086866</td>\n", "      <td>1771</td>\n", "      <td>0.766335</td>\n", "      <td>1.0</td>\n", "      <td>4.0</td>\n", "      <td>8.0</td>\n", "      <td>高频搜索词(&gt;230)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16065</th>\n", "      <td>糖</td>\n", "      <td>2172</td>\n", "      <td>3216</td>\n", "      <td>33359</td>\n", "      <td>2630</td>\n", "      <td>0.078839</td>\n", "      <td>1554</td>\n", "      <td>0.715470</td>\n", "      <td>3.0</td>\n", "      <td>5.0</td>\n", "      <td>9.0</td>\n", "      <td>高频搜索词(&gt;230)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11637</th>\n", "      <td>榴莲</td>\n", "      <td>2107</td>\n", "      <td>4989</td>\n", "      <td>41052</td>\n", "      <td>4120</td>\n", "      <td>0.100361</td>\n", "      <td>1622</td>\n", "      <td>0.769815</td>\n", "      <td>1.0</td>\n", "      <td>4.0</td>\n", "      <td>7.0</td>\n", "      <td>高频搜索词(&gt;230)</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["       query  searched_users  searched_times  impression_cnt  click_cnt  \\\n", "17756     芒果            7886           27021          307947      23861   \n", "14027     牛奶            7292           12940          273657      15666   \n", "6779      安佳            6380           15938          121436      11847   \n", "18481     草莓            6216           21333          236832      18117   \n", "19224     蓝莓            5581           16744          148786      11561   \n", "6139      奶油            5211            9543          262185      10141   \n", "10823     柠檬            5129           10508          157201      11487   \n", "23226     黄油            4608            8282          179807       6590   \n", "7034   安佳淡奶油            4481           11399           74295       8167   \n", "21194     铁塔            3750            9705           45829       7200   \n", "12968    淡奶油            2942            5705          137980       6176   \n", "10993     树莓            2559            5772           45178       3387   \n", "22460   马斯卡彭            2461            5824           45516       4103   \n", "13317    火龙果            2446            6591           62354       4847   \n", "6391      奶酪            2444            4634           68030       3705   \n", "21755     青提            2423            6620           58250       4304   \n", "5956     奥利奥            2383            3944           24469       3177   \n", "14148    牛油果            2313           10785           87537       7604   \n", "16065      糖            2172            3216           33359       2630   \n", "11637     榴莲            2107            4989           41052       4120   \n", "\n", "        sku_ctr  clicked_user_cnt  user_ctr  p50_click_index  p75_click_index  \\\n", "17756  0.077484              6273  0.795561              3.0              8.0   \n", "14027  0.057247              5726  0.785460              9.0             19.0   \n", "6779   0.097558              4644  0.728471              1.0              4.0   \n", "18481  0.076497              4674  0.752173              3.0              8.0   \n", "19224  0.077702              4106  0.735842              2.0              5.0   \n", "6139   0.038679              3574  0.685857             12.0             30.0   \n", "10823  0.073072              4035  0.786703              6.0             11.0   \n", "23226  0.036650              2813  0.610593             10.0             22.0   \n", "7034   0.109927              3469  0.776584              1.0              2.0   \n", "21194  0.157106              2831  0.756750              0.0              1.0   \n", "12968  0.044760              2250  0.764786              9.0             24.0   \n", "10993  0.074970              1559  0.610176              1.0              4.0   \n", "22460  0.090144              1807  0.736649              0.0              2.0   \n", "13317  0.077734              1876  0.767594              3.0              6.0   \n", "6391   0.054461              1674  0.684943              4.0             10.0   \n", "21755  0.073888              1556  0.642444              1.0              4.0   \n", "5956   0.129838              1912  0.803699              1.0              2.0   \n", "14148  0.086866              1771  0.766335              1.0              4.0   \n", "16065  0.078839              1554  0.715470              3.0              5.0   \n", "11637  0.100361              1622  0.769815              1.0              4.0   \n", "\n", "       p90_click_index       搜索频次标签  \n", "17756             12.0  高频搜索词(>230)  \n", "14027             26.0  高频搜索词(>230)  \n", "6779               7.0  高频搜索词(>230)  \n", "18481             16.0  高频搜索词(>230)  \n", "19224             10.0  高频搜索词(>230)  \n", "6139              50.0  高频搜索词(>230)  \n", "10823             16.0  高频搜索词(>230)  \n", "23226             31.0  高频搜索词(>230)  \n", "7034               4.0  高频搜索词(>230)  \n", "21194              3.0  高频搜索词(>230)  \n", "12968             45.0  高频搜索词(>230)  \n", "10993             11.0  高频搜索词(>230)  \n", "22460              6.0  高频搜索词(>230)  \n", "13317              9.0  高频搜索词(>230)  \n", "6391              22.0  高频搜索词(>230)  \n", "21755             10.0  高频搜索词(>230)  \n", "5956               4.0  高频搜索词(>230)  \n", "14148              8.0  高频搜索词(>230)  \n", "16065              9.0  高频搜索词(>230)  \n", "11637              7.0  高频搜索词(>230)  "]}, "execution_count": 23, "metadata": {}, "output_type": "execute_result"}], "source": ["high_frequency_query_df.sort_values(by='searched_users', ascending=False).head(20)"]}, {"cell_type": "code", "execution_count": 24, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>query</th>\n", "      <th>searched_users</th>\n", "      <th>searched_times</th>\n", "      <th>impression_cnt</th>\n", "      <th>click_cnt</th>\n", "      <th>sku_ctr</th>\n", "      <th>clicked_user_cnt</th>\n", "      <th>user_ctr</th>\n", "      <th>p50_click_index</th>\n", "      <th>p75_click_index</th>\n", "      <th>p90_click_index</th>\n", "      <th>搜索频次标签</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>5017</th>\n", "      <td>啵啵</td>\n", "      <td>444</td>\n", "      <td>484</td>\n", "      <td>2862</td>\n", "      <td>37</td>\n", "      <td>0.012928</td>\n", "      <td>32</td>\n", "      <td>0.072072</td>\n", "      <td>4.0</td>\n", "      <td>11.0</td>\n", "      <td>20.0</td>\n", "      <td>高频搜索词(&gt;230)</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["     query  searched_users  searched_times  impression_cnt  click_cnt  \\\n", "5017    啵啵             444             484            2862         37   \n", "\n", "       sku_ctr  clicked_user_cnt  user_ctr  p50_click_index  p75_click_index  \\\n", "5017  0.012928                32  0.072072              4.0             11.0   \n", "\n", "      p90_click_index       搜索频次标签  \n", "5017             20.0  高频搜索词(>230)  "]}, "execution_count": 24, "metadata": {}, "output_type": "execute_result"}], "source": ["high_frequency_query_df[high_frequency_query_df['query']=='啵啵']"]}, {"cell_type": "code", "execution_count": 25, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>query</th>\n", "      <th>searched_users</th>\n", "      <th>searched_times</th>\n", "      <th>impression_cnt</th>\n", "      <th>click_cnt</th>\n", "      <th>sku_ctr</th>\n", "      <th>clicked_user_cnt</th>\n", "      <th>user_ctr</th>\n", "      <th>p50_click_index</th>\n", "      <th>p75_click_index</th>\n", "      <th>p90_click_index</th>\n", "      <th>搜索频次标签</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>21922</th>\n", "      <td>面包</td>\n", "      <td>202</td>\n", "      <td>200</td>\n", "      <td>4833</td>\n", "      <td>74</td>\n", "      <td>0.015311</td>\n", "      <td>51</td>\n", "      <td>0.252475</td>\n", "      <td>21.5</td>\n", "      <td>28.00</td>\n", "      <td>45.4</td>\n", "      <td>中频搜索词</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7666</th>\n", "      <td>寒天</td>\n", "      <td>193</td>\n", "      <td>223</td>\n", "      <td>1981</td>\n", "      <td>40</td>\n", "      <td>0.020192</td>\n", "      <td>35</td>\n", "      <td>0.181347</td>\n", "      <td>9.0</td>\n", "      <td>11.00</td>\n", "      <td>23.5</td>\n", "      <td>中频搜索词</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8195</th>\n", "      <td>巧克力酱</td>\n", "      <td>183</td>\n", "      <td>196</td>\n", "      <td>4421</td>\n", "      <td>94</td>\n", "      <td>0.021262</td>\n", "      <td>63</td>\n", "      <td>0.344262</td>\n", "      <td>10.0</td>\n", "      <td>26.75</td>\n", "      <td>38.7</td>\n", "      <td>中频搜索词</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10071</th>\n", "      <td>明治</td>\n", "      <td>178</td>\n", "      <td>197</td>\n", "      <td>1804</td>\n", "      <td>44</td>\n", "      <td>0.024390</td>\n", "      <td>33</td>\n", "      <td>0.185393</td>\n", "      <td>4.5</td>\n", "      <td>16.25</td>\n", "      <td>24.7</td>\n", "      <td>中频搜索词</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3600</th>\n", "      <td>勺子</td>\n", "      <td>158</td>\n", "      <td>216</td>\n", "      <td>1813</td>\n", "      <td>196</td>\n", "      <td>0.108108</td>\n", "      <td>130</td>\n", "      <td>0.822785</td>\n", "      <td>2.0</td>\n", "      <td>4.00</td>\n", "      <td>12.0</td>\n", "      <td>中频搜索词</td>\n", "    </tr>\n", "    <tr>\n", "      <th>22803</th>\n", "      <td>鲜沐</td>\n", "      <td>157</td>\n", "      <td>168</td>\n", "      <td>838</td>\n", "      <td>17</td>\n", "      <td>0.020286</td>\n", "      <td>15</td>\n", "      <td>0.096154</td>\n", "      <td>3.0</td>\n", "      <td>4.00</td>\n", "      <td>4.4</td>\n", "      <td>中频搜索词</td>\n", "    </tr>\n", "    <tr>\n", "      <th>23571</th>\n", "      <td>黑白全脂牛奶</td>\n", "      <td>152</td>\n", "      <td>213</td>\n", "      <td>2058</td>\n", "      <td>162</td>\n", "      <td>0.078717</td>\n", "      <td>95</td>\n", "      <td>0.625000</td>\n", "      <td>1.0</td>\n", "      <td>7.00</td>\n", "      <td>13.0</td>\n", "      <td>中频搜索词</td>\n", "    </tr>\n", "    <tr>\n", "      <th>23273</th>\n", "      <td>黄油安佳</td>\n", "      <td>151</td>\n", "      <td>217</td>\n", "      <td>2468</td>\n", "      <td>154</td>\n", "      <td>0.062399</td>\n", "      <td>110</td>\n", "      <td>0.728477</td>\n", "      <td>2.0</td>\n", "      <td>5.00</td>\n", "      <td>15.0</td>\n", "      <td>中频搜索词</td>\n", "    </tr>\n", "    <tr>\n", "      <th>23334</th>\n", "      <td>黄油薄脆</td>\n", "      <td>151</td>\n", "      <td>193</td>\n", "      <td>2002</td>\n", "      <td>86</td>\n", "      <td>0.042957</td>\n", "      <td>61</td>\n", "      <td>0.403974</td>\n", "      <td>0.0</td>\n", "      <td>7.00</td>\n", "      <td>22.4</td>\n", "      <td>中频搜索词</td>\n", "    </tr>\n", "    <tr>\n", "      <th>18416</th>\n", "      <td>茶</td>\n", "      <td>148</td>\n", "      <td>200</td>\n", "      <td>6148</td>\n", "      <td>184</td>\n", "      <td>0.029928</td>\n", "      <td>83</td>\n", "      <td>0.560811</td>\n", "      <td>13.0</td>\n", "      <td>26.00</td>\n", "      <td>53.4</td>\n", "      <td>中频搜索词</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11518</th>\n", "      <td>椰汁</td>\n", "      <td>147</td>\n", "      <td>190</td>\n", "      <td>4017</td>\n", "      <td>139</td>\n", "      <td>0.034603</td>\n", "      <td>69</td>\n", "      <td>0.469388</td>\n", "      <td>8.5</td>\n", "      <td>19.00</td>\n", "      <td>51.7</td>\n", "      <td>中频搜索词</td>\n", "    </tr>\n", "    <tr>\n", "      <th>22871</th>\n", "      <td>鲜酵母</td>\n", "      <td>144</td>\n", "      <td>206</td>\n", "      <td>1508</td>\n", "      <td>150</td>\n", "      <td>0.099469</td>\n", "      <td>84</td>\n", "      <td>0.583333</td>\n", "      <td>0.0</td>\n", "      <td>0.00</td>\n", "      <td>2.1</td>\n", "      <td>中频搜索词</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13371</th>\n", "      <td>炼奶</td>\n", "      <td>144</td>\n", "      <td>198</td>\n", "      <td>3042</td>\n", "      <td>173</td>\n", "      <td>0.056870</td>\n", "      <td>106</td>\n", "      <td>0.741259</td>\n", "      <td>9.0</td>\n", "      <td>11.00</td>\n", "      <td>22.0</td>\n", "      <td>中频搜索词</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11143</th>\n", "      <td>桃</td>\n", "      <td>143</td>\n", "      <td>165</td>\n", "      <td>3321</td>\n", "      <td>87</td>\n", "      <td>0.026197</td>\n", "      <td>57</td>\n", "      <td>0.398601</td>\n", "      <td>9.0</td>\n", "      <td>23.00</td>\n", "      <td>34.0</td>\n", "      <td>中频搜索词</td>\n", "    </tr>\n", "    <tr>\n", "      <th>19703</th>\n", "      <td>蛋黄</td>\n", "      <td>140</td>\n", "      <td>210</td>\n", "      <td>2100</td>\n", "      <td>130</td>\n", "      <td>0.061905</td>\n", "      <td>76</td>\n", "      <td>0.542857</td>\n", "      <td>2.0</td>\n", "      <td>4.50</td>\n", "      <td>15.0</td>\n", "      <td>中频搜索词</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13299</th>\n", "      <td>火腿</td>\n", "      <td>139</td>\n", "      <td>199</td>\n", "      <td>2745</td>\n", "      <td>157</td>\n", "      <td>0.057195</td>\n", "      <td>77</td>\n", "      <td>0.553957</td>\n", "      <td>4.0</td>\n", "      <td>10.00</td>\n", "      <td>19.6</td>\n", "      <td>中频搜索词</td>\n", "    </tr>\n", "    <tr>\n", "      <th>21617</th>\n", "      <td>雪媚娘皮</td>\n", "      <td>139</td>\n", "      <td>206</td>\n", "      <td>2924</td>\n", "      <td>116</td>\n", "      <td>0.039672</td>\n", "      <td>65</td>\n", "      <td>0.467626</td>\n", "      <td>4.0</td>\n", "      <td>8.00</td>\n", "      <td>16.0</td>\n", "      <td>中频搜索词</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13735</th>\n", "      <td>爆珠</td>\n", "      <td>138</td>\n", "      <td>222</td>\n", "      <td>2670</td>\n", "      <td>160</td>\n", "      <td>0.059925</td>\n", "      <td>103</td>\n", "      <td>0.746377</td>\n", "      <td>1.0</td>\n", "      <td>4.00</td>\n", "      <td>8.0</td>\n", "      <td>中频搜索词</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5738</th>\n", "      <td>大米粉</td>\n", "      <td>137</td>\n", "      <td>222</td>\n", "      <td>1359</td>\n", "      <td>167</td>\n", "      <td>0.122884</td>\n", "      <td>97</td>\n", "      <td>0.713235</td>\n", "      <td>1.0</td>\n", "      <td>1.00</td>\n", "      <td>3.0</td>\n", "      <td>中频搜索词</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1576</th>\n", "      <td>三麟苏打汽水</td>\n", "      <td>136</td>\n", "      <td>212</td>\n", "      <td>1095</td>\n", "      <td>181</td>\n", "      <td>0.165297</td>\n", "      <td>113</td>\n", "      <td>0.837037</td>\n", "      <td>0.0</td>\n", "      <td>0.00</td>\n", "      <td>0.0</td>\n", "      <td>中频搜索词</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["        query  searched_users  searched_times  impression_cnt  click_cnt  \\\n", "21922      面包             202             200            4833         74   \n", "7666       寒天             193             223            1981         40   \n", "8195     巧克力酱             183             196            4421         94   \n", "10071      明治             178             197            1804         44   \n", "3600       勺子             158             216            1813        196   \n", "22803      鲜沐             157             168             838         17   \n", "23571  黑白全脂牛奶             152             213            2058        162   \n", "23273    黄油安佳             151             217            2468        154   \n", "23334    黄油薄脆             151             193            2002         86   \n", "18416       茶             148             200            6148        184   \n", "11518      椰汁             147             190            4017        139   \n", "22871     鲜酵母             144             206            1508        150   \n", "13371      炼奶             144             198            3042        173   \n", "11143       桃             143             165            3321         87   \n", "19703      蛋黄             140             210            2100        130   \n", "13299      火腿             139             199            2745        157   \n", "21617    雪媚娘皮             139             206            2924        116   \n", "13735      爆珠             138             222            2670        160   \n", "5738      大米粉             137             222            1359        167   \n", "1576   三麟苏打汽水             136             212            1095        181   \n", "\n", "        sku_ctr  clicked_user_cnt  user_ctr  p50_click_index  p75_click_index  \\\n", "21922  0.015311                51  0.252475             21.5            28.00   \n", "7666   0.020192                35  0.181347              9.0            11.00   \n", "8195   0.021262                63  0.344262             10.0            26.75   \n", "10071  0.024390                33  0.185393              4.5            16.25   \n", "3600   0.108108               130  0.822785              2.0             4.00   \n", "22803  0.020286                15  0.096154              3.0             4.00   \n", "23571  0.078717                95  0.625000              1.0             7.00   \n", "23273  0.062399               110  0.728477              2.0             5.00   \n", "23334  0.042957                61  0.403974              0.0             7.00   \n", "18416  0.029928                83  0.560811             13.0            26.00   \n", "11518  0.034603                69  0.469388              8.5            19.00   \n", "22871  0.099469                84  0.583333              0.0             0.00   \n", "13371  0.056870               106  0.741259              9.0            11.00   \n", "11143  0.026197                57  0.398601              9.0            23.00   \n", "19703  0.061905                76  0.542857              2.0             4.50   \n", "13299  0.057195                77  0.553957              4.0            10.00   \n", "21617  0.039672                65  0.467626              4.0             8.00   \n", "13735  0.059925               103  0.746377              1.0             4.00   \n", "5738   0.122884                97  0.713235              1.0             1.00   \n", "1576   0.165297               113  0.837037              0.0             0.00   \n", "\n", "       p90_click_index 搜索频次标签  \n", "21922             45.4  中频搜索词  \n", "7666              23.5  中频搜索词  \n", "8195              38.7  中频搜索词  \n", "10071             24.7  中频搜索词  \n", "3600              12.0  中频搜索词  \n", "22803              4.4  中频搜索词  \n", "23571             13.0  中频搜索词  \n", "23273             15.0  中频搜索词  \n", "23334             22.4  中频搜索词  \n", "18416             53.4  中频搜索词  \n", "11518             51.7  中频搜索词  \n", "22871              2.1  中频搜索词  \n", "13371             22.0  中频搜索词  \n", "11143             34.0  中频搜索词  \n", "19703             15.0  中频搜索词  \n", "13299             19.6  中频搜索词  \n", "21617             16.0  中频搜索词  \n", "13735              8.0  中频搜索词  \n", "5738               3.0  中频搜索词  \n", "1576               0.0  中频搜索词  "]}, "execution_count": 25, "metadata": {}, "output_type": "execute_result"}], "source": ["medium_df.sort_values(by='searched_users', ascending=False).head(20)"]}, {"cell_type": "code", "execution_count": 26, "metadata": {}, "outputs": [], "source": ["system_prompt=\"\"\"你是一位专业的电商搜索引擎优化分析师，专注于餐饮供应链领域。你的任务是分析点击率低的搜索词，找出问题原因并提供改进策略。\n", "\n", "分析对象：轻饮食行业供应链平台（主营烘焙、茶饮、咖啡等轻饮食行业的原料，包括鲜果、乳制品、面粉、西餐原材料等餐饮门店物料）\n", "\n", "当用户提供以下信息时：\n", "1. 低点击率的搜索词\n", "2. 该搜索词获得的搜索结果（商品SKU信息,前60个,实际上用户可能会看到比60个还要多的内容,我们仅需要基于这60个来分析）\n", "\n", "你需要进行深度分析并提供以下内容：\n", "1. 搜索词与结果的匹配度分析：\n", "   - 深度回顾该搜索词的搜索表现（用户会给出数据，请你完全依据用户的数据来深度解析）\n", "   - 语义匹配程度\n", "   - 商品类别是否符合搜索意图\n", "   - 搜索词是否存在歧义或多义性\n", "\n", "2. 搜索结果质量评估：\n", "   - 结果数量是否充足\n", "   - 商品描述是否清晰准确\n", "   - 价格竞争力分析\n", "   - 是否有库存信息显示\n", "\n", "3. 用户意图解析：\n", "   - 用户可能的真实需求是什么\n", "   - 搜索词背后的使用场景分析\n", "   - 行业特定术语vs通用词汇的使用差异\n", "\n", "4. 具体问题诊断（选择适用的）：\n", "   - 关键词匹配问题（如：ES词库缺失、同义词匹配缺失）\n", "   - 商品缺失问题（平台缺少相关商品）\n", "   - 结果排序问题（相关度算法调整需求）\n", "   - 季节性或时效性问题\n", "   - 行业术语与日常用语差异问题\n", "   - 商品属性标注不完善问题\n", "\n", "5. 改进策略建议（针对性提供）：\n", "   - 词库优化方案（添加同义词、近义词、行业术语映射等）\n", "   - 商品上新建议（具体指出缺失的SKU类型）\n", "   - 搜索结果排序算法调整建议\n", "   - 商品信息优化建议（标题、描述、规格等）\n", "   - 用户引导方式建议（搜索提示、热门搜索等）\n", "\n", "请提供具体、可操作的分析和建议，避免笼统的回答。针对餐饮供应链的特性，考虑B2B客户的专业采购需求与习惯。\"\"\""]}, {"cell_type": "code", "execution_count": 27, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["模块初始化，开始加载数据...\n", "area_df 的 SQLite 数据已过时\n", "category_df 的 SQLite 数据已过时\n", "sku_sales_volume_df 的 SQLite 数据已过时\n", "部分或全部本地数据无效，将从ODPS重新加载。\n", "开始重新加载所有数据...\n", "从 ODPS 获取 area_df\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-07-30 17:05:43 - INFO - Tunnel session created: <InstanceDownloadSession id=20250730170543d9f7c20b080857a4 project_name=summerfarm_ds_dev instance_id=20250730090541454gnlozpqgl9>\n", "2025-07-30 17:05:43 - INFO - sql:\n", "select area_no, area_name from summerfarm_tech.ods_area_df\n", "        where ds=max_pt('summerfarm_tech.ods_area_df')\n", "columns:Index(['area_no', 'area_name'], dtype='object')\n"]}, {"name": "stdout", "output_type": "stream", "text": ["已将 area_df 保存到 SQLite\n", "从 ODPS 获取 category_df\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-07-30 17:05:50 - INFO - Tunnel session created: <InstanceDownloadSession id=202507301705504af8c20b0807941b project_name=summerfarm_ds_dev instance_id=20250730090543947gmqq2ecmg7>\n", "2025-07-30 17:05:52 - INFO - sql:\n", "\n", "    select CONCAT(category2,'_',category3,'_',category4) category,sku_id\n", "    from summerfarm_tech.dim_sku_df\n", "    where ds=max_pt('summerfarm_tech.dim_sku_df')\n", "    group by category,sku_id\n", "    \n", "columns:Index(['category', 'sku_id'], dtype='object')\n"]}, {"name": "stdout", "output_type": "stream", "text": ["已将 category_df 保存到 SQLite\n", "从 ODPS 获取 sku_sales_volume_df\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-07-30 17:06:00 - INFO - Tunnel session created: <InstanceDownloadSession id=202507301706009bde321a0708d71e project_name=summerfarm_ds_dev instance_id=20250730090552400gh4skoiodpw5>\n", "2025-07-30 17:06:01 - INFO - sql:\n", "\n", "    SELECT  ds,sku\n", "            ,SUM(amount) AS sales_volume\n", "            ,round(SUM(actual_total_price),2) AS total_gmv\n", "    FROM    summerfarm_tech.ods_order_item_df\n", "    WHERE   ds = MAX_PT('summerfarm_tech.ods_order_item_df')\n", "    AND     add_time >= '2025-06-30 00:00:00'\n", "    GROUP BY sku,ds;\n", "    \n", "columns:Index(['ds', 'sku', 'sales_volume', 'total_gmv'], dtype='object')\n"]}, {"name": "stdout", "output_type": "stream", "text": ["已将 sku_sales_volume_df 保存到 SQLite\n", "所有数据重新加载完成。\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-07-30 17:06:01 - INFO - response header: {'Date': 'Wed, 30 Jul 2025 09:06:01 GMT', 'Content-Type': 'application/json;charset=UTF-8', 'Transfer-Encoding': 'chunked', 'Connection': 'keep-alive', 'Vary': 'Accept-Encoding', 'eagleeye-traceid': 'd8ce205ad6c9a7b63a497ea951f5ad62', 'x-server-rt': '100', 'Strict-Transport-Security': 'max-age=15724800; includeSubDomains', 'Content-Encoding': 'gzip'}\n"]}, {"name": "stdout", "output_type": "stream", "text": ["从 SQLite 加载 area_df\n", "从 SQLite 加载 area_df\n", "从 SQLite 加载 area_df\n", "从 SQLite 加载 area_df\n", "从 SQLite 加载 area_df\n", "从 SQLite 加载 area_df\n", "从 SQLite 加载 area_df\n", "从 SQLite 加载 area_df\n", "从 SQLite 加载 area_df\n", "从 SQLite 加载 area_df\n", "从 SQLite 加载 area_df\n", "[{'monthly_sales': 7, 'monthly_gmv': 905.76, 'area_price': '杭州, ¥132.0', 'sku_id': '666522036138, 1KG*12包/原味', 'sku': '666522036138', 'spu_name': '德馨寒天晶球', 'pd_id': '8796', 'img_url': 'https://azure.summerfarm.net/picture-path/mi8hff1izcdg5pvqd.jpg?imageslim=3', 'brand': '德馨', 'weight': '1KG*12包/原味', 'sort_score': 2068.266, 'properties': {'品牌': '德馨', '储藏温度': '置于阴凉干燥处', '产地': '中国'}, 'category_id': 666, 'sufficient_stock': True, 'category': '成品原料_果冻类配料_波波丨晶球'}, {'monthly_sales': 0, 'monthly_gmv': 0.0, 'area_price': '杭州, ¥139.0', 'sku_id': '666522036064, 1KG*12包/黑糖味', 'sku': '666522036064', 'spu_name': '德馨寒天晶球', 'pd_id': '8796', 'img_url': 'https://azure.summerfarm.net/picture-path/mi8hff1izcdg5pvqd.jpg?imageslim=3', 'brand': '德馨', 'weight': '1KG*12包/黑糖味', 'sort_score': 2128.4854, 'properties': {'品牌': '德馨', '储藏温度': '置于阴凉干燥处', '产地': '中国'}, 'category_id': 666, 'sufficient_stock': True, 'category': '成品原料_果冻类配料_波波丨晶球'}, {'monthly_sales': 0, 'monthly_gmv': 0.0, 'area_price': '杭州, ¥139.9', 'sku_id': '666522036600, 1KG*12包/樱花味', 'sku': '666522036600', 'spu_name': '德馨寒天晶球', 'pd_id': '8796', 'img_url': 'https://azure.summerfarm.net/picture-path/mi8hff1izcdg5pvqd.jpg?imageslim=3', 'brand': '德馨', 'weight': '1KG*12包/樱花味', 'sort_score': 2128.4854, 'properties': {'品牌': '德馨', '储藏温度': '置于阴凉干燥处', '产地': '中国'}, 'category_id': 666, 'sufficient_stock': False, 'category': '成品原料_果冻类配料_波波丨晶球'}, {'monthly_sales': 0, 'monthly_gmv': 0.0, 'area_price': '杭州, ¥139.9', 'sku_id': '666522036112, 1KG*12包/水蜜桃味', 'sku': '666522036112', 'spu_name': '德馨寒天晶球', 'pd_id': '8796', 'img_url': 'https://azure.summerfarm.net/picture-path/mi8hff1izcdg5pvqd.jpg?imageslim=3', 'brand': '德馨', 'weight': '1KG*12包/水蜜桃味', 'sort_score': 2098.1973, 'properties': {'品牌': '德馨', '储藏温度': '置于阴凉干燥处', '产地': '中国'}, 'category_id': 666, 'sufficient_stock': False, 'category': '成品原料_果冻类配料_波波丨晶球'}, {'monthly_sales': 0, 'monthly_gmv': 0.0, 'area_price': '杭州, ¥139.9', 'sku_id': '666522036772, 1KG*12包/乳酸菌味', 'sku': '666522036772', 'spu_name': '德馨寒天晶球', 'pd_id': '8796', 'img_url': 'https://azure.summerfarm.net/picture-path/mi8hff1izcdg5pvqd.jpg?imageslim=3', 'brand': '德馨', 'weight': '1KG*12包/乳酸菌味', 'sort_score': 2077.6821, 'properties': {'品牌': '德馨', '储藏温度': '置于阴凉干燥处', '产地': '中国'}, 'category_id': 666, 'sufficient_stock': False, 'category': '成品原料_果冻类配料_波波丨晶球'}]\n"]}], "source": ["import pandas as pd\n", "import json\n", "import os\n", "from search_xianmu_product import search_xianmu_product\n", "\n", "\n", "def analytics_with_r1(\n", "    query: str = \"寒天\",\n", "    csv_content=\"\",\n", "    low_perf_df: pd.DataFrame = pd.DataFrame(),\n", "):\n", "    perf = low_perf_df[low_perf_df[\"query\"] == query].iloc[0].to_dict()\n", "    data = {\n", "        \"model\": \"ep-20250206185410-zzb56\",\n", "        \"messages\": [\n", "            {\n", "                \"role\": \"system\",\n", "                \"content\": system_prompt,\n", "            },\n", "            {\n", "                \"role\": \"user\",\n", "                \"content\": f\"搜索词:{query}\\n搜索表现:{perf}\\n\\n\\n搜索结果:\\n{csv_content}\",\n", "            },\n", "        ],\n", "        \"stream\": True,\n", "    }\n", "\n", "    try:\n", "        response = requests.post(\n", "            \"https://ark.cn-beijing.volces.com/api/v3/chat/completions\",\n", "            headers={\n", "                \"Content-Type\": \"application/json\",\n", "                \"Authorization\": f\"Bearer {os.getenv('ARK_API_KEY')}\",\n", "            },\n", "            json=data,\n", "            stream=True,\n", "        )\n", "    except requests.RequestException as e:\n", "        print(f\"Request failed: {e}\")\n", "        return\n", "\n", "    final_content = \"\"\n", "    final_reasoning_content = \"\"\n", "    if response.status_code == 200:\n", "        for line in response.iter_lines():\n", "            if line:\n", "                line_content = line.decode(\"utf-8\").strip()\n", "                if line_content.startswith(\"data: \"):\n", "                    try:\n", "                        json_content = line_content[6:]  # Remove \"data: \" prefix\n", "                        if len(json_content) <= 2 or \"[DONE]\" == json_content:\n", "                            # JSON content should be at least {}\n", "                            print(f\"接收到了非JSON数据:{line_content}, 将跳过..\")\n", "                            continue\n", "                        delta_content = json.loads(json_content)\n", "                        if delta_content:\n", "                            choices = delta_content.get(\"choices\")\n", "                            if choices and len(choices) > 0:\n", "                                delta = choices[0].get(\"delta\")\n", "                                if delta:\n", "                                    reasoning_content = delta.get(\n", "                                        \"reasoning_content\", \"\"\n", "                                    )\n", "                                    content = delta.get(\"content\", \"\")\n", "                                    # 如果存在 reasoning_content，则将其添加到 final_reasoning_content 中\n", "                                    if reasoning_content:\n", "                                        final_reasoning_content += reasoning_content\n", "                                        # 使用 end='' 参数来避免 print 函数换行\n", "                                        print(reasoning_content, end=\"\")\n", "                                    # 如果存在 content，则将其添加到 final_content 中\n", "                                    elif content:\n", "                                        final_content += content\n", "                                        print(content, end=\"\")\n", "                    except json.JSONDecodeError as e:\n", "                        print(f\"JSONDecodeError: {e} - Data: {line_content}\")\n", "                        continue  # Skip to the next line if JSON decoding fails\n", "        print(f\"Yielding final content: {final_content}\")\n", "    else:\n", "        print(f\"Error: {response.status_code} - {response.text}\")\n", "        return None, None\n", "\n", "    return (final_reasoning_content, final_content)\n", "\n", "\n", "def analyze_and_report_search_query(query: str, city: str = \"杭州\"):\n", "    \"\"\"\n", "    搜索鲜沐产品，分析搜索结果，并发送飞书通知。\n", "\n", "    Args:\n", "        query (str): 搜索关键词。\n", "        city (str): 搜索城市。\n", "    \"\"\"\n", "    # 搜索鲜沐产品\n", "    searched_items = search_xianmu_product(query=query, city=city, page_size=60)\n", "    # 将搜索结果转换为pandas DataFrame\n", "    searched_items_df = pd.DataFrame(searched_items)\n", "    # 提取'sku_id', 'spu_name', 'brand', 'properties', 'category', 'area_price'列，并将DataFrame写入CSV文件，包含索引\n", "    csv_content = searched_items_df[\n", "        [\"sku_id\", \"spu_name\", \"brand\", \"properties\", \"category\", \"area_price\"]\n", "    ].to_csv(index=True)\n", "    final_reasoning_content, final_content = analytics_with_r1(\n", "        query=query, csv_content=csv_content, low_perf_df=low_perf_df\n", "    )\n", "    send_feishu_notice_with_title_and_content(\n", "        title=f\"‘{query}’搜索词的R1分析报告\", markdown_str=final_content\n", "    )"]}, {"cell_type": "code", "execution_count": 28, "metadata": {}, "outputs": [], "source": ["# 调用函数，执行对每个query在\"杭州\"的搜索分析\n", "# for index, row in low_perf_df.iterrows():\n", "#     analyze_and_report_search_query(query=row[\"query\"])"]}, {"cell_type": "markdown", "metadata": {}, "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.6"}}, "nbformat": 4, "nbformat_minor": 2}
import hashlib
import json
import logging
import requests
from datetime import datetime
import sys
import os

sys.path.append(os.path.dirname(os.path.abspath(__file__)))
from area_and_category_lib import (
    get_area_no_by_name,
    get_category_text,
    get_sku_monthly_sales,
)

token_cache = {}


def get_md5_encoded_string(phone_number, date, word):
    input_string = f"{phone_number}{date}{word}"
    input_bytes = input_string.encode("utf-8")
    md5_hash = hashlib.md5(input_bytes)
    md5_hex = md5_hash.hexdigest()
    return md5_hex


def get_token_for_phone(phone_number: str = "18618107293") -> str:
    today = datetime.now().strftime("%Y%m%d")

    cache_key = f"{phone_number}{today}"
    if cache_key in token_cache:
        logging.info(f"token_cache.get(cache_key):{token_cache.get(cache_key)}")
        return token_cache.get(cache_key)

    word = "login"
    md5_encoded_string = get_md5_encoded_string(phone_number, today, word)
    url = f"https://h5.summerfarm.net/openid?phone={phone_number}&sign={md5_encoded_string}"
    token = requests.get(url=url, timeout=2000, proxies={}).json()
    try:
        token_cache[cache_key] = token["data"]["token"]
        return token_cache.get(cache_key)
    except Exception as e:
        logging.error(f"获取token失败:{e}")
        raise e


# 强制指定线上版本为V2，这样才能便于对比
ab_exp_header = [
    {
        "experimentId": "new-home",
        "experimentPlace": "place-of:new-home",
        "variantId": "V3",
    },
    {
        "experimentId": "product_search_rerank",
        "experimentPlace": "place-of:product_search_rerank",
        "variantId": "V2",
    },
    {
        "experimentId": "product_search_disperse",
        "experimentPlace": "place-of:product_search_disperse",
        "variantId": "V2",
    },
]
ab_exp_header = json.dumps(ab_exp_header)


def search_xianmu_product(query: str, city: str, page_size: int = 6) -> list[dict]:
    token = get_token_for_phone()
    area_no = get_area_no_by_name(city)
    url = 'https://h5.summerfarm.net/mall/sku/page'
    headers = {
        "token": f"{token}",
        "Content-Type": "application/json",
        "xm-ab-exp": ab_exp_header,
    }
    data = {
        "pageIndex": 1,
        "pageSize": page_size,
        "titleSuggest": query,
        "sortBy": 1,
        "sortDirection": 1,
        "lastSortType": 1,
        "conditionInputs": []
    }
    response = requests.post(url=url, headers=headers, json=data, timeout=2000, proxies={})
    logging.info(f"response header: {response.headers}")
    response = response.json()
    product_list = response["data"]["list"]
    if not isinstance(product_list, list) or len(product_list) <= 0:
        return []
    result_list = []
    for product in product_list:
        result = {}
        result["monthly_sales"], result["monthly_gmv"] = get_sku_monthly_sales(
            product.get("sku", "")
        )
        result["area_price"] = (
            f"{city}, ¥{product.get('salePrice', product.get('originalPrice'))}"
        )
        result["sku_id"] = f"{product.get('sku', '')}, {product.get('weight','')}"
        result["sku"] = product.get("sku", "")
        result["spu_name"] = f"{product.get('pdName', '')}"
        result["pd_id"] = f"{product.get('pdId', '')}"
        result["img_url"] = (
            f"https://azure.summerfarm.net/{product['picturePath']}?imageslim=3"
        )
        key_value_list = product["keyValueList"]
        brand = ""
        kv_map = {}
        for key_value in key_value_list:
            if "name" in key_value and "productsPropertyValue" in key_value:
                kv_map[key_value["name"]] = key_value["productsPropertyValue"]
                if key_value["name"] == "品牌":
                    brand = key_value["productsPropertyValue"]
        result["brand"] = brand
        result["weight"] = product["weight"]
        result["sort_score"] = product.get("esScore", 0)
        result["properties"] = kv_map
        result["category_id"] = product["categoryId"]
        result["sufficient_stock"] = product["quantity"] > 0
        result["category"] = get_category_text(sku_id=result["sku"])
        result_list.append(result)

    return result_list


print(search_xianmu_product(query="寒天", city="杭州"))

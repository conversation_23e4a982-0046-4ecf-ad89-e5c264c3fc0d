import requests
from requests.auth import HTTPBasicAuth
import json
import sys
from datetime import datetime

sys.path.append("../")
from area_and_category_lib import get_area_no_by_name, get_sku_monthly_sales, get_category_text, get_query_category_prediction


def _assemble_search_results(hits, city):
    """
    组装搜索结果的通用函数，避免代码重复
    
    :param hits: Elasticsearch返回的hits
    :param city: 城市名称
    :return: 组装后的结果列表
    """
    result_list = []
    for hit in hits:
        source = hit.get("_source", {})
        result = {}
        sku = source.get("itemCode", "")
        result["monthly_sales"], result["monthly_gmv"] = get_sku_monthly_sales(sku)
        result["_score"] = hit.get("_score", "")
        result["sort_score"] = result["_score"]
        result["area_price"] = f"{city}, ¥{source['price']}"
        result["sku_id"] = f'{sku}, {source.get("specification", "")}'
        result["sku"] = sku
        result["spu_name"] = source.get("title", "")
        result["pd_id"] = source.get(
            "marketItemId", source.get("id", "")
        )  # 优先使用marketItemId, 否则使用id
        picture_path = source.get("mainPicture", "404.jpg")
        result["img_url"] = (
            picture_path
            if picture_path.startswith("http")
            else "https://azure.summerfarm.net/" + picture_path
        )+"?imageslim=3"
        result["brand"] = source.get("brandName", "")
        result["weight"] = source.get("specification", "")
        result["store_quantity"] = source.get("storeQuantity", 0)
        result["properties"] = source.get("keyProperty", [])
        result["property_values"] = source.get("propertyValues", [])
        result["category_id"] = source.get("categoryId", "")
        result["sufficient_stock"] = source.get("storeQuantity", 0) > 0
        result["category"] = get_category_text(sku_id=sku)
        category_list = source.get("category", [])
        result["front_category_name"] = category_list
        result_list.append(result)
    return result_list


def search_xianmu_product_directly_from_es(
    query,
    es_host="es-cn-i7m2pv3ht000o90dy.public.elasticsearch.aliyuncs.com:9200",
    es_index="summerfarm_item_info",
    brand_name=None,
    size=60,
    minimum_score=20.0,
    city="杭州",
):
    """
    直接从Elasticsearch搜索鲜沐商品。

    :param query: 搜索关键词。
    :param es_host: Elasticsearch主机地址，默认为公网地址。
    :param es_index: Elasticsearch索引名称，默认为summerfarm_item_info。
    :return: Elasticsearch搜索结果，JSON格式。
    """
    area_no = get_area_no_by_name(city=city)
    es_url = f"http://{es_host}/{es_index}/_search"
    auth = HTTPBasicAuth("elastic", "elastic@Xianmu0619")
    headers = {"Content-Type": "application/json"}
    must = [
        {"term": {"targetId": area_no}},
        {"terms": {"subType": [1, 2, 3, 4]}},  # 添加 subType 的 must 查询
    ]
    if brand_name is not None:
        must.append(
            {"term": {"brandName": brand_name}}
        )  # 这里应该是 brand_name 而不是 query
    search_body = {
        "from": 0,
        "size": size,
        "explain": True,
        "_source": [
            "title",
            "category",
            "brandName",
            "propertyValues",
            "keyProperty",
            "titlePure",
            "titleExt",
            "saleOut",
            "title.keyword",
            "price",
            "specification",
            "mainPicture",
            "itemCode",
            "marketItemId",
            "id",
            "categoryId",
            "storeQuantity",
        ],
        "query": {
            "function_score": {
                "query": {
                    "bool": {
                        "filter": [
                            {"term": {"targetId": area_no}},
                            {"term": {"onSale": 1}},
                            {"term": {"deleteFlag": 1}},
                            {"term": {"marketItemDeleteFlag": 1}},
                            {"term": {"show": 1}},
                            {"term": {"onSaleStrategyType": 3}},
                        ]
                    }
                },
                "functions": [
                    {
                        "filter": {
                            "bool": {
                                "must": [
                                    {"term": {"title": query}},
                                    {"term": {"category.name.keyword": query}},
                                ]
                            }
                        },
                        "weight": 120,
                    },
                    {
                        "filter": {
                            "bool": {
                                "must": {"term": {"title": query}},
                                "must_not": {"term": {"category.name.keyword": query}},
                            }
                        },
                        "weight": 80,
                    },
                    {
                        "filter": {
                            "bool": {
                                "should": [
                                    {"match": {"title": query}},
                                    {"term": {"category.name.keyword": query}},
                                ],
                                "minimum_should_match": 1,
                                "must_not": [{"term": {"title": query}}],
                            }
                        },
                        "weight": 40,
                    },
                    {
                        "filter": {
                            "multi_match": {
                                "query": query,
                                "fields": [
                                    "brandName",
                                    "propertyValues",
                                    "keyProperty",
                                    "titlePure",
                                    "titleExt",
                                ],
                                "analyzer": "xianmu_ik_max_word",
                            }
                        },
                        "field_value_factor": {
                            "field": "_matched",
                            "missing": 0,
                            "factor": 1,
                        },
                    },
                ],
                "score_mode": "sum",
                "boost_mode": "replace",
            }
        },
        "sort": [
            {"_score": {"order": "desc"}},
            {
                "_script": {
                    "type": "number",
                    "script": {
                        "source": "doc['storeQuantity'].value * doc['price'].value"
                    },
                    "order": "desc"
                }
            }
        ],
    }

    try:
        print(f"search_body:\n{json.dumps(search_body, indent=4, ensure_ascii=False)}")
        response = requests.post(
            es_url, auth=auth, headers=headers, data=json.dumps(search_body), timeout=5
        )
        response.raise_for_status()  # 检查请求是否成功
        hits = response.json().get("hits", {}).get("hits", [])
        return _assemble_search_results(hits, city)
    except requests.exceptions.RequestException as e:
        print(f"Elasticsearch搜索请求失败: {e}")
        return []


def search_custom_score_from_es(
    query,
    es_host="es-cn-i7m2pv3ht000o90dy.public.elasticsearch.aliyuncs.com:9200",
    es_index="summerfarm_item_info",
    brand_name=None,
    size=60,
    minimum_score=20.0,
    city="杭州",
):
    """
    使用自定义打分规则从Elasticsearch搜索鲜沐商品。
    
    打分规则：
    1、120分：query 在title中term匹配、且前端类目名称term匹配、且brandName term匹配（需要同时满足三个条件）
    2、100分：query 在title中term匹配、且前端类目名称term匹配（不匹配120分的其他条件）
    3、80分：query 在title中term匹配，且不符合1和2
    4、60分：query 在title中match匹配（"analyzer": "xianmu_ik_max_word"），且前端类目名称term匹配
    5、40分：不符合1、2、3、4，且query在title中match匹配（"analyzer": "xianmu_ik_max_word"）、或者query在brandName中match匹配
    6、其他则全部1分

    :param query: 搜索关键词。
    :param es_host: Elasticsearch主机地址，默认为公网地址。
    :param es_index: Elasticsearch索引名称，默认为summerfarm_item_info。
    :return: Elasticsearch搜索结果，JSON格式。
    """
    area_no = get_area_no_by_name(city=city)
    es_url = f"http://{es_host}/{es_index}/_search"
    auth = HTTPBasicAuth("elastic", "elastic@Xianmu0619")
    headers = {"Content-Type": "application/json"}
    
    # 获取查询的类目预测
    category_prediction = get_query_category_prediction(query)
    predicted_front_category_names = category_prediction.get("front_category_name_arr", [])
    
    must = [
        {"term": {"targetId": area_no}},
        {"terms": {"subType": [1, 2, 3, 4]}},  # 添加 subType 的 must 查询
    ]
    if brand_name is not None:
        must.append(
            {"term": {"brandName": brand_name}}
        )
    
    # 构建function_score查询的functions列表
    functions = []
    
    # 规则1：120分 - query在title中term匹配，且前端类目名称term匹配，且brandName term匹配
    if predicted_front_category_names:
        for front_category_name in predicted_front_category_names:
            functions.append({
                "filter": {
                    "bool": {
                        "must": [
                            {"term": {"title": query}},
                            {"term": {"category.name.keyword": front_category_name}},
                            {"term": {"brandName": query}}
                        ]
                    }
                },
                "weight": 120
            })
    
    # 规则2：100分 - query在title中term匹配，且前端类目名称term匹配
    if predicted_front_category_names:
        for front_category_name in predicted_front_category_names:
            functions.append({
                "filter": {
                    "bool": {
                        "must": [
                            {"term": {"title": query}},
                            {"term": {"category.name.keyword": front_category_name}}
                        ],
                        "must_not": [{
                            "bool": {
                                "must": [
                                    {"term": {"title": query}},
                                    {"term": {"category.name.keyword": front_category_name}},
                                    {"term": {"brandName": query}}
                                ]
                            }
                        }]
                    }
                },
                "weight": 100
            })
    
    # 规则3：80分 - query在title中term匹配，但不符合规则1和2
    must_not_condition_80 = []
    if predicted_front_category_names:
        for front_category_name in predicted_front_category_names:
            must_not_condition_80.append({"term": {"category.name.keyword": front_category_name}})
    
    functions.append({
        "filter": {
            "bool": {
                "must": [{"term": {"title": query}}],
                "must_not": [
                    {"term": {"brandName": query}},  # 排除brandName匹配
                    *must_not_condition_80  # 排除规则2的情况
                ]
            }
        },
        "weight": 80
    })
    
    # 规则4：60分 - query在title中match匹配，且前端类目名称term匹配
    if predicted_front_category_names:
        for front_category_name in predicted_front_category_names:
            functions.append({
                "filter": {
                    "bool": {
                        "must": [
                            {"match": {"title": {"query": query, "analyzer": "xianmu_ik_max_word"}}},
                            {"term": {"category.name.keyword": front_category_name}}
                        ],
                        "must_not": [{"term": {"title": query}}]  # 排除已经term匹配的
                    }
                },
                "weight": 60
            })
    
    # 规则5：40分 - query在title中match匹配或在brandName中match匹配（不符合前面规则的）
    must_not_condition_40 = [{"term": {"title": query}}]  # 排除term匹配
    if predicted_front_category_names:
        for front_category_name in predicted_front_category_names:
            must_not_condition_40.append({"term": {"category.name.keyword": front_category_name}})
    
    functions.append({
        "filter": {
            "bool": {
                "should": [
                    {
                        "bool": {
                            "must": [{"match": {"title": {"query": query, "analyzer": "xianmu_ik_max_word"}}}],
                            "must_not": must_not_condition_40
                        }
                    },
                    {"match": {"brandName": {"query": query, "analyzer": "xianmu_ik_max_word"}}}
                ],
                "minimum_should_match": 1
            }
        },
        "weight": 40
    })
    
    search_body = {
        "from": 0,
        "size": size,
        "explain": True,
        "_source": [
            "title",
            "category",
            "brandName",
            "propertyValues",
            "keyProperty",
            "titlePure",
            "titleExt",
            "saleOut",
            "title.keyword",
            "price",
            "specification",
            "mainPicture",
            "itemCode",
            "marketItemId",
            "id",
            "categoryId",
            "storeQuantity",
        ],
        "query": {
            "function_score": {
                "query": {
                    "bool": {
                        "should": [
                            {"match": {"title": {"query": query, "analyzer": "xianmu_ik_max_word"}}},
                            {"match": {"brandName": {"query": query, "analyzer": "xianmu_ik_max_word"}}},
                            {"term": {"title": query}},
                            {"term": {"brandName": query}}
                        ],
                        "minimum_should_match": 1,
                        "filter": [
                            {"term": {"targetId": area_no}},
                            {"term": {"onSale": 1}},
                            {"term": {"deleteFlag": 1}},
                            {"term": {"marketItemDeleteFlag": 1}},
                            {"term": {"show": 1}},
                            {"term": {"onSaleStrategyType": 3}},
                        ]
                    }
                },
                "functions": functions,
                "score_mode": "max",  # 使用最高分，确保每个商品只获得一个分数
                "boost_mode": "replace",
                "min_score": 1  # 其他情况默认1分
            }
        },
        "sort": [
            {"_score": {"order": "desc"}},
            {
                "_script": {
                    "type": "number",
                    "script": {
                        "source": "doc['storeQuantity'].value * doc['price'].value"
                    },
                    "order": "desc"
                }
            }
        ],
    }

    try:
        print(f"search_body:\n{json.dumps(search_body, indent=4, ensure_ascii=False)}")
        response = requests.post(
            es_url, auth=auth, headers=headers, data=json.dumps(search_body), timeout=5
        )
        response.raise_for_status()  # 检查请求是否成功
        hits = response.json().get("hits", {}).get("hits", [])
        return _assemble_search_results(hits, city)
    except requests.exceptions.RequestException as e:
        print(f"Elasticsearch搜索请求失败: {e}")
        return []


# Example usage:
if __name__ == "__main__":
    # 热门搜索词列表
    hot_queries = [
        {"query": "芒果", "ctr": 0.3372, "search_count": 43885},
        {"query": "牛奶", "ctr": 0.3957, "search_count": 22742},
        {"query": "柠檬", "ctr": 0.3385, "search_count": 25528},
        {"query": "草莓", "ctr": 0.3615, "search_count": 24997},
        {"query": "蓝莓", "ctr": 0.3402, "search_count": 19633},
        {"query": "安佳", "ctr": 0.3261, "search_count": 13256},
        {"query": "奶油", "ctr": 0.245, "search_count": 16206},
        {"query": "西瓜", "ctr": 0.3099, "search_count": 21979},
        {"query": "荔枝", "ctr": 0.3133, "search_count": 13739},
        {"query": "鸡蛋", "ctr": 0.3138, "search_count": 9977},
        {"query": "铁塔", "ctr": 0.3543, "search_count": 6173},
        {"query": "黄油", "ctr": 0.2238, "search_count": 9434},
        {"query": "青提", "ctr": 0.3313, "search_count": 8969},
        {"query": "树莓", "ctr": 0.1887, "search_count": 7663},
        {"query": "安佳淡奶油", "ctr": 0.3673, "search_count": 6347},
        {"query": "橙子", "ctr": 0.3011, "search_count": 9721},
        {"query": "百香果", "ctr": 0.3852, "search_count": 6750},
        {"query": "牛油果", "ctr": 0.4158, "search_count": 9570},
        {"query": "火龙果", "ctr": 0.3294, "search_count": 7223},
        {"query": "芝士", "ctr":0.1889, "search_count":3002},
        {"query": "茶", "ctr":0.1577, "search_count":379},
    ]
    
    # 生成markdown报告
    markdown_content = []
    markdown_content.append("# 热门搜索词自定义打分效果分析报告\n")
    markdown_content.append(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
    markdown_content.append("## 搜索词概览\n")
    
    # 添加搜索词概览表格
    markdown_content.append("| 搜索词 | CTR | 搜索次数 | 测试结果数量 |")
    markdown_content.append("|--------|-----|----------|--------------|")
    
    total_results = 0
    successful_queries = 0
    
    for query_info in hot_queries:
        query = query_info["query"]
        ctr = query_info["ctr"]
        search_count = query_info["search_count"]
        
        print(f"\n正在测试搜索词: {query}")
        
        # 使用自定义打分搜索
        try:
            results = search_custom_score_from_es(query, size=40)
            result_count = len(results)
            total_results += result_count
            
            if results:
                successful_queries += 1
                status = f"✅ {result_count}个结果"
            else:
                status = "❌ 无结果"
                
            markdown_content.append(f"| {query} | {ctr:.4f} | {search_count:,} | {status} |")
            
            if results:
                # 获取类目预测信息
                category_prediction = get_query_category_prediction(query)
                predicted_category = category_prediction.get("category4", "无预测") or "无预测"
                predicted_category_id = category_prediction.get("category4_id", "无预测") or "无预测"
                predicted_category_percentile = category_prediction.get("category_percentile", 0)
                front_category_names = category_prediction.get("front_category_name_arr", [])
                front_categories_text = " || ".join(front_category_names) if front_category_names else "无预测"
                
                # 添加详细搜索结果
                markdown_content.append(f"\n## 搜索词: [{query}](http://xianmuai.s7.tunnelfrp.com/search-arena/?query={query}&city=杭州&page_size=40)")
                markdown_content.append(f"- **CTR**: {ctr:.4f}")
                markdown_content.append(f"- **搜索次数**: {search_count:,}")
                markdown_content.append(f"- **返回结果数**: {result_count}")
                markdown_content.append(f"- **类目预测**: {predicted_category} (ID: {predicted_category_id}) - 置信度: {predicted_category_percentile:.2f}")
                markdown_content.append(f"- **前端类目**: {front_categories_text}\n")
                
                # 结果表格
                markdown_content.append("| 排名 | 标题 | 分数 | 品牌 | 库存 | 类目ID | 价格 | 规格 | SKU |")
                markdown_content.append("|------|------|------|------|------|--------|------|------|-----|")
                
                for i, result in enumerate(results[:40], 1):
                    try:
                        title = (result.get("spu_name") or "").replace("|", "｜")  # 转义管道符
                        score = result.get("_score") or 0
                        brand = (result.get("brand") or "").replace("|", "｜")
                        stock = result.get("store_quantity") or 0
                        category_id = str(result.get("category_id") or "")
                        price = (result.get("area_price") or "").replace("杭州, ¥", "¥")
                        specification = (result.get("weight") or "").replace("|", "｜")  # weight字段包含规格信息
                        sku = str(result.get("sku") or "")
                        
                        markdown_content.append(
                            f"| {i} | {title[:50]}{'...' if len(title) > 50 else ''} | "
                            f"{score:.1f} | {brand[:15]}{'...' if len(brand) > 15 else ''} | "
                            f"{stock} | {category_id} | {price} | {specification[:20]}{'...' if len(specification) > 20 else ''} | {sku} |"
                        )
                    except Exception as row_error:
                        print(f"处理结果行 {i} 时出错: {row_error}")
                        print(f"结果数据: {result}")
                        # 添加一个简化的行
                        markdown_content.append(f"| {i} | 数据错误 | 0.0 | - | 0 | - | - | - | - |")
                
                markdown_content.append("\n---\n")
                
        except Exception as e:
            import traceback
            print(f"搜索失败: {query}, 错误: {e}")
            print("详细错误信息:")
            traceback.print_exc()
            markdown_content.append(f"| {query} | {ctr:.4f} | {search_count:,} | ❌ 搜索失败 |")
    
    # 添加总结
    markdown_content.append(f"\n## 测试总结\n")
    markdown_content.append(f"- **总搜索词数**: {len(hot_queries)}")
    markdown_content.append(f"- **成功搜索词数**: {successful_queries}")
    markdown_content.append(f"- **总结果数**: {total_results}")
    markdown_content.append(f"- **平均每词结果数**: {total_results/len(hot_queries):.1f}")
    
    # 写入markdown文件
    filename = f"热门搜索词自定义打分测试报告_{datetime.now().strftime('%Y%m%d')}.md"
    
    try:
        with open(filename, 'w', encoding='utf-8') as f:
            f.write('\n'.join(markdown_content))
        print(f"\n✅ 测试报告已生成: {filename}")
        print(f"📊 测试完成: {successful_queries}/{len(hot_queries)} 个搜索词成功，共 {total_results} 个结果")
    except Exception as e:
        print(f"❌ 写入文件失败: {e}")
        # 如果写入失败，至少打印部分结果
        print("\n部分测试结果:")
        print('\n'.join(markdown_content[:50]))
